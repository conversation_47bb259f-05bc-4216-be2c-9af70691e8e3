<template>
  <view class="page-container">
    <scroll-view class="main-scroll-view" scroll-y>
      <HeaderComponent />

      <view>
        <BannerComponent />
      </view>

      <view>
        <QuickNavigationComponent/>
      </view>

      <view>
        <CountryHighlightComponent/>
      </view>

      <view>
        <EventPromotionComponent/>
      </view>

      <view>
        <NewsListComponent/>
      </view>

      <view>
        <ActivityGridComponent/>
      </view>

      <view class="no-more-divider">
        <view class="no-more-line"></view>
        <text class="no-more-text">没有更多了</text>
        <view class="no-more-line"></view>
      </view>

    </scroll-view>

    <CustomTabBar :current="0"/>

    <!-- 动态绑定客服图标 -->
    <view class="fab-customer-service" @click="navigateToService">
      <image class="fab-icon" :src="fabIconUrl" mode="aspectFit"></image>
    </view>

    <!-- 多个弹窗广告组件 - 只显示当前的一个 -->
    <PopupAdComponent
        v-if="showPopupAd && currentAdData"
        :show="showPopupAd"
        :ad-data="currentAdData"
        @close="handlePopupClose"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue';
import { onShow, onLoad } from '@dcloudio/uni-app';
import { getAdListByPositionApi } from '@/api/platform/ad.js';
import HeaderComponent from "@/components/home/<USER>";
import BannerComponent from '@/components/home/<USER>';
import QuickNavigationComponent from '@/components/home/<USER>';
import CountryHighlightComponent from '@/components/home/<USER>';
import NewsListComponent from "@/components/home/<USER>";
import ActivityGridComponent from '@/components/home/<USER>';
import EventPromotionComponent from '@/components/home/<USER>';
import PopupAdComponent from '@/components/common/PopupAdComponent.vue';
import CustomTabBar from '@/components/layout/CustomTabBar.vue';

// --- 状态定义 ---

// 定义客服图标的URL（仅暗号读取）
const fabIconUrl = ref('');

// 弹窗广告相关
const showPopupAd = ref(false);
const adList = ref([]); // 存储所有广告数据
const currentAdIndex = ref(0); // 当前显示的广告索引
const currentAdData = ref(null); // 当前显示的广告数据
const AD_POSITION_CODE = 'SPLASH_SCREEN';

// 会话标记 - 用于判断是否是本次会话首次进入首页
let hasShownInCurrentSession = false;

// --- 方法定义 ---

const checkAndShowPopupAd = async () => {
  try {
    // 如果本次会话已经显示过广告，则不再显示
    if (hasShownInCurrentSession) {
      console.log('本次会话已经显示过弹屏广告，不再显示');
      return;
    }

    const response = await getAdListByPositionApi(AD_POSITION_CODE, { pageSize: 10 });

    console.log('弹窗广告API返回结果:', response);

    if (response && response.data && response.data.length > 0) {
      adList.value = response.data;
      currentAdIndex.value = 0;

      console.log(`获取到 ${adList.value.length} 个广告，开始显示第一个`);

      // 显示第一个广告
      showNextAd();
    } else {
      console.log('没有广告数据可显示');
      // 即使没有广告数据，也标记为已显示
      hasShownInCurrentSession = true;
    }
  } catch (error) {
    console.error('获取弹窗广告失败:', error.message || error);
    // 请求失败也标记为已显示
    hasShownInCurrentSession = true;
  }
};

// 显示下一个广告
const showNextAd = () => {
  if (currentAdIndex.value < adList.value.length) {
    currentAdData.value = adList.value[currentAdIndex.value];
    showPopupAd.value = true;

    console.log(`显示第 ${currentAdIndex.value + 1} 个广告:`, currentAdData.value.title);
  } else {
    console.log('所有广告已显示完毕');
    showPopupAd.value = false;
    currentAdData.value = null;
  }
};

// 处理弹窗关闭
const handlePopupClose = () => {
  showPopupAd.value = false;
  currentAdIndex.value++;

  // 检查是否所有广告都显示完了
  if (currentAdIndex.value >= adList.value.length) {
    // 所有广告都显示完毕，标记本次会话为已显示
    hasShownInCurrentSession = true;
    console.log('所有广告显示完毕，已标记本次会话为已显示');
    return;
  }

  // 延迟一下再显示下一个广告，避免太突兀
  setTimeout(() => {
    showNextAd();
  }, 300);
};

// 模拟小程序重启（测试用）
const simulateAppRestart = () => {
  hasShownInCurrentSession = false;
  console.log('已重置会话状态，模拟小程序重启');
  uni.showToast({
    title: '已模拟重启',
    icon: 'success'
  });
  // 重新检查广告
  checkAndShowPopupAd();
};

const navigateToService = () => {
  uni.navigateTo({
    url: '/pages_sub/pages_profile/contact'
  });
};

// --- 生命周期钩子 ---

// onLoad 只在页面首次加载时触发，适合判断是否是小程序冷启动
onLoad(() => {
  console.log('首页 onLoad - 页面首次加载');
  // onLoad时不重置状态，因为可能是从其他页面返回
});

onShow(() => {
  console.log('首页 onShow - 页面显示', {
    hasShownInCurrentSession: hasShownInCurrentSession
  });

  // 隐藏原生tabbar
  uni.hideTabBar();

  // 检查并显示弹窗广告
  checkAndShowPopupAd();

  // 每次页面显示时，从全局缓存读取正确的静态资源数据
  const assets = uni.getStorageSync('staticAssets');
  fabIconUrl.value = assets?.fab_customer_service_icon || '';
});
</script>

<style lang="scss" scoped>
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #FFFFFF;
  overflow: hidden;
}

.main-scroll-view {
  flex: 1;
  height: 0;
  position: relative;
  z-index: 0;
  transform: translateZ(0);
  padding-top: 176rpx;
  padding-bottom: calc(144rpx + env(safe-area-inset-bottom));
  box-sizing: border-box;

  :deep(.u-loadmore) {
    display: none !important;
  }
}

.fab-customer-service {
  position: fixed;
  right: 20rpx;
  bottom: calc(200rpx + env(safe-area-inset-bottom));
  width: 100rpx;
  height: 100rpx;
  background-color: #FFFFFF;
  border-radius: 50%;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
  transition: opacity 0.3s;
}

.fab-customer-service:active {
  opacity: 0.7;
}

.fab-icon {
  width: 60rpx;
  height: 60rpx;
}

.no-more-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 0;
}

.no-more-text {
  width: 120rpx;
  height: 34rpx;
  line-height: 34rpx;
  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;
  font-weight: normal;
  font-size: 24rpx;
  color: #9B9A9A;
  text-align: center;
  font-style: normal;
  text-transform: none;
}

.no-more-line {
  width: 40rpx;
  height: 2rpx;
  background: #CBCBCB;
  border-radius: 0rpx;
  margin: 0 12rpx;
  flex-shrink: 0;
}
</style>