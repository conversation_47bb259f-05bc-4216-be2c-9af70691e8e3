<template>
  <view v-if="articleList.length > 0" class="news-list-container">
    <view class="section-header">
      <view class="title-wrapper">
        <text class="title-main">{{ titleParts.main }}</text>
        <text class="title-gradient">{{ titleParts.gradient }}</text>
      </view>
      <view class="more-link" @click="goToArticleListPage">
        <text>更多</text>
        <u-icon name="arrow-right" size="14" color="#9B9A9A"></u-icon>
      </view>
    </view>

    <view class="tabs-container">
      <u-tabs
          :list="tagList"
          :current="currentTabIndex"
          keyName="name"
          @change="handleTabChange"
          lineColor="#023F98"
          :lineHeight="4"
          :lineWidth="40"
          :activeStyle="{
            color: '#23232A',
            fontSize: '30rpx',
            fontWeight: 'bold'
          }"
          :inactiveStyle="{
            color: '#9B9A9A',
            fontSize: '30rpx'
          }"
      ></u-tabs>
    </view>

    <view class="article-list">
      <view class="article-card" v-for="item in articleList" :key="item.id" @click="gotoDetail(item.id)">
        <view class="card-cover">
          <u-image
              :src="getFullImageUrl(item.coverImageUrl)"
              width="336rpx"
              height="192rpx"
              radius="12rpx"
              :lazy-load="true"
          ></u-image>
        </view>
        <view class="card-content">
          <text class="card-title">{{ item.title }}</text>
          <view class="card-meta">
            <text class="meta-source">{{ item.source }}</text>
            <text class="meta-date">{{ formatDate(item.publishTime) }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { getArticleList } from '@/api/content/article.js';
import { listAllTag } from '@/api/content/tag.js';
import { getFullImageUrl } from '@/utils/image.js';

// --- Props ---
const props = defineProps({
  title: {
    type: String,
    default: '为你推荐'
  },
  params: {
    type: Object,
    default: () => ({
      pageNum: 1,
      pageSize: 4,
      orderByColumn: 'publish_time',
      isAsc: 'desc'
    })
  }
});

// --- 组件状态 ---
const articleList = ref([]);
const tagList = ref([]);
const currentTabIndex = ref(0);
const queryParams = ref({ ...props.params });

// --- Computed ---
const titleParts = computed(() => {
  const title = props.title;
  if (title.length > 2) {
    return {
      main: title.slice(0, 2),
      gradient: title.slice(2)
    };
  }
  return { main: title, gradient: '' };
});

// --- 方法 ---
const loadTags = async () => {
  try {
    const response = await listAllTag();
    const backendTags = Array.isArray(response.data) ? response.data : [];
    tagList.value = [{ id: null, name: '全部' }, ...backendTags.slice(0, 4)];
  } catch (error) {
    console.error('加载标签列表失败:', error);
  }
};

const loadArticles = async (isRefresh = false) => {
  if (isRefresh) {
    queryParams.value.pageNum = 1;
  }
  try {
    const response = await getArticleList(queryParams.value);
    articleList.value = response.rows;
  } catch (error) {
    console.error('加载资讯列表失败:', error);
  }
};

const handleTabChange = (tab) => {
  currentTabIndex.value = tab.index;
  queryParams.value.tagIds = tagList.value[tab.index].id;
  loadArticles(true);
};

const gotoDetail = (id) => {
  uni.navigateTo({
    url: `/pages_sub/pages_article/detail?id=${id}`
  });
};

const goToArticleListPage = () => {
  uni.switchTab({
    url: '/pages/article/index'
  });
};

const formatDate = (dateString) => {
  if (!dateString) return '';
  return dateString.split(' ')[0];
};

onMounted(() => {
  loadTags();
  loadArticles(true);
});
</script>

<style lang="scss" scoped>
.news-list-container {
  margin-top: 2rpx;
  background-color: #FFFFFF;
  padding: 32rpx 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #FFFFFF;
  padding: 0;
  margin-bottom: 32rpx;
}

.title-wrapper {
  display: flex;
  align-items: center;
  margin-left: 30rpx;
}
.title-main {
  /* "为你"的样式 */
  font-size: 40rpx;
  font-weight: 400; 
  color: #023F98;
  font-family: 'YouSheBiaoTiHei', 'Alibaba PuHuiTi 3.0', sans-serif;
}
.title-gradient {
  /* "推荐"的样式 */
  font-size: 40rpx;
  font-weight: 400;
  background-image: linear-gradient(91.61deg, #FFAD22 0%, #FFBB87 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-family: 'YouSheBiaoTiHei', 'Alibaba PuHuiTi 3.0', sans-serif;
}
.more-link {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #9B9A9A;
}

.tabs-container {
  position: relative;
  background-color: #FFFFFF;
  box-shadow: 0 4rpx 10rpx rgba(0,0,0,0.02), 0 -4rpx 10rpx rgba(0,0,0,0.02);
  margin-left: -24rpx;
  margin-right: -24rpx;
  padding-left: 24rpx;
  padding-right: 24rpx;
  // 用伪元素创建真正铺满屏幕的横线
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100vw; // 延伸到屏幕外
    right: -100vw; // 延伸到屏幕外
    height: 1rpx;
    background-color: #F0F2F5;
    z-index: 1;
  }

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: -100vw; // 延伸到屏幕外
    right: -100vw; // 延伸到屏幕外
    height: 1rpx;
    background-color: #F0F2F5;
    z-index: 1;
  }
}

.article-list {
  margin: 0 24rpx;
}

.article-card {
  display: flex;
  gap: 30rpx;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #F0F2F5;

  &:last-child {
    border-bottom: none;
  }
}

.card-cover {
  flex-shrink: 0;
  width: 336rpx;
  height: 192rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #f3f4f6;
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-width: 0;
}

.card-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #23232A;
  line-height: 1.5;

  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  line-clamp: 2; /* 兼容标准属性 */
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
  font-size: 24rpx;
  color: #9B9A9A;
}
</style>