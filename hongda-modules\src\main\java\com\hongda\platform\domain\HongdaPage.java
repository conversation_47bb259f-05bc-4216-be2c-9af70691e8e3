package com.hongda.platform.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.hongda.common.annotation.Excel;
import com.hongda.common.core.domain.BaseEntity;

/**
 * 页面管理对象 hongda_page
 * 
 * <AUTHOR>
 * @date 2025-08-15
 */
public class HongdaPage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 页面ID, 主键 */
    private Long id;

    /** 页面标题 */
    @Excel(name = "页面标题")
    private String title;

    /** 页面类型 */
    @Excel(name = "页面类型")
    private String pageType;

    /** 富文本内容 */
    private String content;

    /** 目标链接 */
    @Excel(name = "目标链接")
    private String targetUrl;

    /** 排序 */
    @Excel(name = "排序")
    private Long sortOrder;

    /** 状态 */
    @Excel(name = "状态")
    private Integer status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }

    public void setPageType(String pageType) 
    {
        this.pageType = pageType;
    }

    public String getPageType() 
    {
        return pageType;
    }

    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }

    public void setTargetUrl(String targetUrl) 
    {
        this.targetUrl = targetUrl;
    }

    public String getTargetUrl() 
    {
        return targetUrl;
    }

    public void setSortOrder(Long sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Long getSortOrder() 
    {
        return sortOrder;
    }

    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("title", getTitle())
            .append("pageType", getPageType())
            .append("content", getContent())
            .append("targetUrl", getTargetUrl())
            .append("sortOrder", getSortOrder())
            .append("status", getStatus())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
