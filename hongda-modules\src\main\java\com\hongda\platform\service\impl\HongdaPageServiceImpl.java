package com.hongda.platform.service.impl;

import java.util.List;
import com.hongda.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.hongda.platform.mapper.HongdaPageMapper;
import com.hongda.platform.domain.HongdaPage;
import com.hongda.platform.service.IHongdaPageService;

/**
 * 页面管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-15
 */
@Service
public class HongdaPageServiceImpl implements IHongdaPageService 
{
    @Autowired
    private HongdaPageMapper hongdaPageMapper;

    /**
     * 查询页面管理
     * 
     * @param id 页面管理主键
     * @return 页面管理
     */
    @Override
    public HongdaPage selectHongdaPageById(Long id)
    {
        return hongdaPageMapper.selectHongdaPageById(id);
    }

    /**
     * 查询页面管理列表
     * 
     * @param hongdaPage 页面管理
     * @return 页面管理
     */
    @Override
    public List<HongdaPage> selectHongdaPageList(HongdaPage hongdaPage)
    {
        return hongdaPageMapper.selectHongdaPageList(hongdaPage);
    }

    /**
     * 新增页面管理
     * 
     * @param hongdaPage 页面管理
     * @return 结果
     */
    @Override
    public int insertHongdaPage(HongdaPage hongdaPage)
    {
        hongdaPage.setCreateTime(DateUtils.getNowDate());
        return hongdaPageMapper.insertHongdaPage(hongdaPage);
    }

    /**
     * 修改页面管理
     * 
     * @param hongdaPage 页面管理
     * @return 结果
     */
    @Override
    public int updateHongdaPage(HongdaPage hongdaPage)
    {
        hongdaPage.setUpdateTime(DateUtils.getNowDate());
        return hongdaPageMapper.updateHongdaPage(hongdaPage);
    }

    /**
     * 批量删除页面管理
     * 
     * @param ids 需要删除的页面管理主键
     * @return 结果
     */
    @Override
    public int deleteHongdaPageByIds(Long[] ids)
    {
        return hongdaPageMapper.deleteHongdaPageByIds(ids);
    }

    /**
     * 删除页面管理信息
     * 
     * @param id 页面管理主键
     * @return 结果
     */
    @Override
    public int deleteHongdaPageById(Long id)
    {
        return hongdaPageMapper.deleteHongdaPageById(id);
    }
}
