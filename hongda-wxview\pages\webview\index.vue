<template>
  <view>
    <web-view v-if="webviewUrl" :src="webviewUrl"></web-view>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import { onLoad } from '@dcloudio/uni-app';

const webviewUrl = ref('');

onLoad((options) => {
  if (options && options.url) {
    // 解码从URL参数中获取的网页链接
    webviewUrl.value = decodeURIComponent(options.url);

    // [关键修改]：我们不再需要在这里手动设置标题。
    // <web-view> 组件会自动抓取网页的标题并设置它。
    // 这样就避免了标题的二次跳变。

  } else {
    console.error('No url provided for webview.');
    uni.showToast({
      title: '链接地址无效',
      icon: 'error',
      duration: 2000
    });
    // 如果没有有效的URL，可以将标题设置为错误提示
    uni.setNavigationBarTitle({
      title: '页面加载失败'
    });
  }
});
</script>

<style scoped>
/* You can add styles here if needed */
</style>