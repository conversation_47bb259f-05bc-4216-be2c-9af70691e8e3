"use strict";
const uni_modules_uviewPlus_libs_config_config = require("../../libs/config/config.js");
const {
  color
} = uni_modules_uviewPlus_libs_config_config.config;
const Icon = {
  // icon组件
  icon: {
    name: "",
    color: color["u-content-color"],
    size: "16px",
    bold: false,
    index: "",
    hoverClass: "",
    customPrefix: "uicon",
    label: "",
    labelPos: "right",
    labelSize: "15px",
    labelColor: color["u-content-color"],
    space: "3px",
    imgMode: "",
    width: "",
    height: "",
    top: 0,
    stop: false
  }
};
exports.Icon = Icon;
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/uni_modules/uview-plus/components/u-icon/icon.js.map
