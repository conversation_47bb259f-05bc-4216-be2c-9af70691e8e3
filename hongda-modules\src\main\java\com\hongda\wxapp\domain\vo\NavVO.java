package com.hongda.wxapp.domain.vo;

import com.hongda.common.annotation.OssUrl;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.data.annotation.Transient;

import java.io.Serializable;

@Getter
@Setter
@ToString
@Schema(description = "小程序导航配置")
public class NavVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 导航ID */
    @Schema(description = "导航ID", example = "1")
    private Long id;

    /** 导航标题 */
    @Schema(description = "导航标题", example = "活动报名")
    private String title;

    /** 图标链接 */
    @Schema(description = "图标链接", example = "https://example.com/icon.png")
    @OssUrl
    private String iconUrl;

    /** 关联的内容页面ID */
    @Schema(description = "关联的内容页面ID", example = "101")
    private Integer pageId;

    /** 关联的页面标题 (非数据表字段) */
    @Schema(description = "关联的页面标题 (仅用于列表展示)")
    @Transient
    private String pageTitle;
}