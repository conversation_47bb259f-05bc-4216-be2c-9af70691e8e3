{"version": 3, "file": "index.js", "sources": ["pages/profile/index.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHJvZmlsZS9pbmRleC52dWU"], "sourcesContent": ["<template>\n  <view class=\"my-page\">\n    <!-- 顶部背景和头像/登录按钮区域 -->\n<view class=\"header-bg\">\n    <!-- 背景图片 -->\n    <image class=\"header-bg-image\" :src=\"headerBgUrl\" mode=\"aspectFill\"></image>\n    <view class=\"user-profile-box\">\n        <view class=\"avatar-container\">\n            <up-avatar \n                :size=\"54\"  \n                :src=\"isLoggedIn && userInfo && userInfo.avatarUrl ? userInfo.avatarUrl : defaultAvatarUrl\"\n                @click=\"!isLoggedIn ? goToLogin : null\" \n            ></up-avatar>\n            <!-- 覆盖在头像上的透明按钮：微信端触发头像选择器 -->\n            <button\n              v-if=\"isLoggedIn\"\n              class=\"avatar-choose-btn\"\n              open-type=\"chooseAvatar\"\n              @chooseavatar=\"onChooseAvatar\"\n            ></button>\n        </view>\n        <view class=\"user-text-box\">\n            <view v-if=\"isLoggedIn\" class=\"user-info-container\">\n                <view class=\"username-row\">\n                    <text class=\"username-text\">{{ getUserDisplayName }}</text>\n                    <image class=\"edit-icon\" :src=\"editIconUrl\" mode=\"aspectFit\" @click=\"handleEditNickname\"></image>\n                </view>\n            </view>\n            <view v-else class=\"login-button-container\">\n                <text class=\"username-text\" @click=\"goToLogin\">请登录</text>\n            </view>\n        </view>\n    </view>\n\n    <!-- 报名订单卡片 -->\n    <view class=\"order-card-container\" @click=\"handleOrderCardClick\">\n        <!-- 背景图片 -->\n        <image class=\"order-card-bg-image\" :src=\"orderCardBgUrl\" mode=\"aspectFill\"></image>\n        <view class=\"order-card-content\">\n            <view class=\"order-card-left\">\n                <text class=\"order-card-text\">报名订单</text>\n            </view>\n            <view class=\"order-card-right\">\n                <view class=\"order-card-action-wrapper\">\n                    <text class=\"order-card-action\">查看</text>\n                    <image class=\"order-card-arrow\" :src=\"orderArrowUrl\" mode=\"aspectFit\"></image>\n                </view>\n            </view>\n        </view>\n    </view>\n</view>\n\n    <!-- 简化菜单区域 -->\n    <view class=\"menu-list-card\">\n      <up-cell-group :border=\"false\">\n        <!-- 绑定手机号，根据登录状态显示值 -->\n        <up-cell title=\"绑定手机号\" :value=\"getPhoneDisplay\" :isLink=\"false\" :border=\"false\">\n          <template #icon>\n            <image class=\"menu-icon\" :src=\"phoneIconUrl\" mode=\"aspectFit\"></image>\n          </template>\n        </up-cell>\n        <up-cell title=\"隐私政策\" :isLink=\"true\" arrow-direction=\"right\" :border=\"false\" @click=\"handleNavigate('/pages_sub/pages_other/policy?type=privacy_policy')\">\n          <template #icon>\n            <image class=\"menu-icon\" :src=\"privacyIconUrl\" mode=\"aspectFit\"></image>\n          </template>\n        </up-cell>\n        <up-cell title=\"用户协议\" :isLink=\"true\" arrow-direction=\"right\" :border=\"false\" @click=\"handleNavigate('/pages_sub/pages_other/policy?type=user_agreement')\">\n          <template #icon>\n            <image class=\"menu-icon\" :src=\"contractIconUrl\" mode=\"aspectFit\"></image>\n          </template>\n        </up-cell>\n        <up-cell title=\"注销账号\" :isLink=\"true\" arrow-direction=\"right\" :border=\"false\" @click=\"handleDeleteAccountClick\">\n          <template #icon>\n            <image class=\"menu-icon\" :src=\"deleteIconUrl\" mode=\"aspectFit\"></image>\n          </template>\n        </up-cell>\n      </up-cell-group>\n    </view>\n\t\n\t<view v-if=\"isLoggedIn\" class=\"logout-button-wrapper\">\n\t  <view class=\"custom-logout-btn\" @click=\"logout\">\n\t    退出登录\n\t  </view>\n\t</view>\n    \n    <!-- 自定义底部导航栏 -->\n    <CustomTabBar :current=\"4\" />\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed } from 'vue'\nimport { onShow, onLoad } from '@dcloudio/uni-app'\nimport CustomTabBar from '@/components/layout/CustomTabBar.vue'\nimport { deleteAccountApi, updateUserInfoApi } from '@/api/data/user.js'\nimport { BASE_URL } from '@/utils/config.js'\n\nconst MAX_NICKNAME_LENGTH = 15\n\n// 定义页面名称\ndefineOptions({\n  name: 'ProfileIndex'\n})\n\n// 响应式数据\nconst isLoggedIn = ref(false) // 初始登录状态\nconst userInfo = ref(null) // 用户信息\n// 默认头像与其他图片URL （仅暗号映射，不再使用本地兜底）\nconst defaultAvatarUrl = ref('')\nconst headerBgUrl = ref('')\nconst editIconUrl = ref('') \nconst orderArrowUrl = ref('') \nconst phoneIconUrl = ref('') \nconst contractIconUrl = ref('') \nconst privacyIconUrl = ref('') \nconst deleteIconUrl = ref('') \nconst orderCardBgUrl = ref('') \n\n// 解析静态资源缓存：仅读取缓存暗号\nconst resolveAssetUrl = (assetKey) => {\n  const assets = uni.getStorageSync('staticAssets')\n  return (assets && assets[assetKey]) ? assets[assetKey] : ''\n}\n\n// 刷新默认头像\nconst refreshDefaultAvatar = () => {\n  defaultAvatarUrl.value = resolveAssetUrl('default_avatar')\n}\n\n// 刷新页面内其余静态资源\nconst refreshProfileAssets = () => {\n  headerBgUrl.value = resolveAssetUrl('mybg')\n  editIconUrl.value = resolveAssetUrl('my_edit')\n  orderArrowUrl.value = resolveAssetUrl('group_right')\n  phoneIconUrl.value = resolveAssetUrl('my_phone')\n  contractIconUrl.value = resolveAssetUrl('my_contract')\n  privacyIconUrl.value = resolveAssetUrl('my_personal')\n  deleteIconUrl.value = resolveAssetUrl('my_delete')\n  orderCardBgUrl.value = resolveAssetUrl('order-card-bg')\n}\n\n// 计算属性\nconst getUserDisplayName = computed(() => {\n  if (!userInfo.value) {\n    return '用户' // 默认显示\n  }\n  \n  // 优先显示昵称，其次显示手机号，最后显示默认名称\n  if (userInfo.value.nickname) {\n    return userInfo.value.nickname\n  }\n  \n  if (userInfo.value.phoneNumber) {\n    // 如果有手机号，显示格式化的手机号\n    const phone = userInfo.value.phoneNumber\n    if (phone.length === 11) {\n      return phone.substring(0, 3) + '****' + phone.substring(7)\n    }\n    return phone\n  }\n  \n  return '用户' // 默认显示\n})\n\nconst getPhoneDisplay = computed(() => {\n  if (!isLoggedIn.value) {\n    return '' // 未登录时不显示文案\n  }\n  \n  // 检查手机号字段（可能是phone或phoneNumber）\n  const phone = userInfo.value?.phone || userInfo.value?.phoneNumber\n  \n  if (!phone) {\n    return '未绑定' // 已登录但没有手机号\n  }\n  \n  // 格式化手机号显示（中间4位用*号替换）\n  if (phone.length === 11) {\n    return phone.substring(0, 3) + '****' + phone.substring(7)\n  }\n  \n  // 如果手机号格式不标准，直接显示\n  return phone\n})\n\n// 方法定义\n// 统一的导航守卫方法\nconst handleNavigate = (url) => {\n  // 检查登录状态\n  if (!isLoggedIn.value) {\n    // 未登录时，跳转到登录页\n    console.log('用户未登录，跳转到登录页')\n    goToLogin()\n    return\n  }\n  \n  // 已登录时，正常跳转到目标页面\n  console.log('用户已登录，跳转到:', url)\n  uni.navigateTo({\n    url: url,\n    fail: (err) => {\n      console.error('页面跳转失败:', err)\n      uni.showToast({\n        title: '页面跳转失败',\n        icon: 'none',\n        duration: 2000\n      })\n    }\n  })\n}\n\n// 报名订单卡片点击处理\nconst handleOrderCardClick = () => {\n  if (!isLoggedIn.value) {\n    console.log('点击报名订单卡片，需要先登录')\n    goToLogin()\n  } else {\n    console.log('点击报名订单卡片，跳转到订单页面')\n    uni.navigateTo({\n      url: '/pages_sub/pages_profile/orders',\n      fail: (err) => {\n        console.error('跳转订单页面失败:', err)\n        uni.showToast({\n          title: '页面跳转失败',\n          icon: 'none',\n          duration: 2000\n        })\n      }\n    })\n  }\n}\n\n\n// 注销账号点击处理\nconst handleDeleteAccountClick = () => {\n  if (!isLoggedIn.value) {\n    console.log('注销账号需要先登录')\n    goToLogin()\n    return\n  }\n  \n  // 已登录时，显示注销确认弹窗\n  uni.showModal({\n    title: '注销账号',\n    content: '注销后账号将被标记为已注销，确定要继续吗？',\n    confirmText: '确定注销',\n    cancelText: '取消',\n    success: (res) => {\n      if (res.confirm) {\n        // 用户确认注销，调用注销方法\n        console.log('用户确认注销账号')\n        confirmDeleteAccount()\n      }\n    }\n  })\n}\n\n// 确认注销账号的实际逻辑\nconst confirmDeleteAccount = async () => {\n  console.log('=== 开始执行注销账号流程 ===')\n  \n  // 显示加载提示\n  uni.showLoading({ title: '正在注销...' })\n  \n  try {\n    console.log('调用后端注销接口...')\n    const result = await deleteAccountApi()\n    \n    console.log('注销接口调用成功:', result)\n    \n    // 关闭加载提示\n    uni.hideLoading()\n    \n    // 显示成功提示\n    uni.showToast({\n      title: '账号已成功注销',\n      icon: 'success',\n      duration: 2000\n    })\n    \n    // 清空本地数据但不显示额外提示（注销成功提示已经显示过了）\n    clearUserDataSilently()\n    \n    console.log('注销账号流程完成')\n    \n  } catch (error) {\n    console.error('注销账号过程中发生错误:', error)\n    \n    // 关闭加载提示\n    uni.hideLoading()\n    \n    // 显示错误提示\n    uni.showToast({\n      title: '注销失败，请稍后再试',\n      icon: 'none',\n      duration: 3000\n    })\n    \n    // 显示详细错误信息（开发调试用）\n    console.error('注销失败详情:', {\n      message: error.message,\n      stack: error.stack\n    })\n  }\n}\n\n// 检查登录状态的方法\nconst checkLoginStatus = () => {\n  // 检查实际的token和用户信息\n  const token = uni.getStorageSync('token')\n  const userInfoData = uni.getStorageSync('userInfo')\n  \n  console.log('从本地存储获取的token:', token)\n  console.log('从本地存储获取的userInfo:', userInfoData)\n  \n  const newLoginStatus = !!token\n  console.log('计算出的登录状态:', newLoginStatus)\n  console.log('当前页面登录状态:', isLoggedIn.value)\n  \n  // 强制更新登录状态和用户信息\n  isLoggedIn.value = newLoginStatus\n  userInfo.value = userInfoData || null\n  \n  if (isLoggedIn.value && userInfoData) {\n    // 如果已登录且有用户信息，可以在这里设置用户数据\n    console.log('用户已登录，用户信息:', userInfoData)\n    if (userInfoData.phoneNumber) {\n      console.log('用户手机号:', userInfoData.phoneNumber)\n    }\n  } else if (isLoggedIn.value && !userInfoData) {\n    console.log('有token但无用户信息')\n  } else {\n    console.log('用户未登录')\n  }\n}\n\n// 点击\"请登录\"按钮跳转到登录页面\nconst goToLogin = () => {\n  uni.navigateTo({\n    url: '/pages_sub/pages_other/login'\n  })\n}\n\n// 点击\"退出登录\"按钮\nconst logout = (isDeleteAccount = false) => {\n  // 如果是注销账号操作，直接清除数据，不显示确认弹窗\n  if (isDeleteAccount) {\n    clearUserData('已退出登录')\n    return\n  }\n  \n  // 正常退出登录时显示确认弹窗\n  uni.showModal({\n    title: '提示',\n    content: '确定要退出登录吗？',\n    success: (res) => {\n      if (res.confirm) {\n        clearUserData('已退出登录')\n      }\n    }\n  })\n}\n\n// 编辑昵称点击处理\nconst handleEditNickname = () => {\n  if (!isLoggedIn.value) {\n    console.log('编辑昵称需要先登录')\n    goToLogin()\n    return\n  }\n  \n  // 弹出输入框让用户输入新昵称\n  uni.showModal({\n    title: '修改昵称',\n    editable: true,\n    placeholderText: '请输入新昵称',\n    content: userInfo.value?.nickname || '',\n    success: async (res) => {\n      if (res.confirm && res.content && res.content.trim()) {\n        const newNickname = res.content.trim()\n        \n        // 检查昵称长度\n        if (newNickname.length > MAX_NICKNAME_LENGTH) {\n          uni.showToast({\n            title: `昵称不能超过${MAX_NICKNAME_LENGTH}个字符`,\n            icon: 'none',\n            duration: 2000\n          })\n          return\n        }\n        \n        // 调用API更新昵称\n        await updateNickname(newNickname)\n      }\n    }\n  })\n}\n\n// 更新昵称的方法\nconst updateNickname = async (newNickname) => {\n  console.log('=== 开始更新昵称 ===')\n\n  // 再次校验长度，双重保障\n  if (!newNickname || newNickname.trim().length === 0) {\n    uni.showToast({\n      title: '昵称不能为空',\n      icon: 'none',\n      duration: 2000\n    })\n    return\n  }\n  if (newNickname.length > MAX_NICKNAME_LENGTH) {\n    uni.showToast({\n      title: `昵称不能超过${MAX_NICKNAME_LENGTH}个字符`,\n      icon: 'none',\n      duration: 2000\n    })\n    return\n  }\n  \n  // 显示加载提示\n  uni.showLoading({ title: '正在更新...' })\n  \n  try {\n    console.log('调用updateUserInfoApi更新昵称:', newNickname)\n    const result = await updateUserInfoApi({\n      nickname: newNickname\n    })\n    \n    console.log('昵称更新接口调用成功:', result)\n    \n    // 关闭加载提示\n    uni.hideLoading()\n    \n    // 更新本地用户信息\n    if (userInfo.value) {\n      userInfo.value.nickname = newNickname\n      // 同时更新本地存储\n      uni.setStorageSync('userInfo', userInfo.value)\n    }\n    \n    // 显示成功提示\n    uni.showToast({\n      title: '昵称修改成功',\n      icon: 'success',\n      duration: 2000\n    })\n    \n    console.log('昵称更新完成')\n    \n  } catch (error) {\n    console.error('更新昵称过程中发生错误:', error)\n    \n    // 关闭加载提示\n    uni.hideLoading()\n    \n    // 显示错误提示\n    uni.showToast({\n      title: error.message || '更新失败，请稍后再试',\n      icon: 'none',\n      duration: 3000\n    })\n    \n    console.error('昵称更新失败详情:', {\n      message: error.message,\n      stack: error.stack\n    })\n  }\n}\n\n// 清除用户数据的统一方法\nconst clearUserData = (message) => {\n  console.log('=== 开始清除用户数据 ===')\n  \n  // 清除token和用户信息\n  uni.removeStorageSync('token')\n  uni.removeStorageSync('userInfo')\n  \n  // 更新页面状态\n  isLoggedIn.value = false\n  userInfo.value = null\n  \n  // 显示提示信息\n  if (message) {\n    uni.showToast({\n      title: message,\n      icon: 'success',\n      duration: 1500\n    })\n  }\n  \n  console.log('用户数据已清除，UI已更新')\n}\n\n// 静默清除用户数据（不显示提示）\nconst clearUserDataSilently = () => {\n  console.log('=== 静默清除用户数据 ===')\n  \n  // 清除token和用户信息\n  uni.removeStorageSync('token')\n  uni.removeStorageSync('userInfo')\n  \n  // 更新页面状态\n  isLoggedIn.value = false\n  userInfo.value = null\n  \n  console.log('用户数据已静默清除，UI已更新')\n}\n\n// 已移除模拟登录相关逻辑\n\n// 生命周期钩子\nonShow(() => {\n  // 页面显示时隐藏原生 tabBar\n  uni.hideTabBar();\n  // 添加小延迟确保数据已保存\n  setTimeout(() => {\n    checkLoginStatus()\n    refreshDefaultAvatar()\n    refreshProfileAssets()\n  }, 100)\n})\n\nonLoad(() => {\n  checkLoginStatus()\n  refreshDefaultAvatar()\n  refreshProfileAssets()\n})\n\n// 选择头像回调（仅微信端可用）\nconst onChooseAvatar = (e) => {\n  const tempPath = e?.detail?.avatarUrl\n  if (!tempPath) return\n  uploadAvatar(tempPath)\n}\n\n// 上传并保存头像\nconst uploadAvatar = async (filePath) => {\n  if (!isLoggedIn.value) {\n    goToLogin()\n    return\n  }\n  const token = uni.getStorageSync('token')\n  if (!token) {\n    goToLogin()\n    return\n  }\n\n  uni.showLoading({ title: '上传中...' })\n  try {\n    await new Promise((resolve, reject) => {\n      uni.uploadFile({\n        url: BASE_URL + '/common/upload',\n        filePath,\n        name: 'file',\n        header: {\n          Authorization: 'Bearer ' + token\n        },\n        success: async (res) => {\n          try {\n            const data = JSON.parse(res.data || '{}')\n            if (res.statusCode === 200 && data.code === 200 && data.url) {\n              const url = data.url\n              await updateUserInfoApi({ avatarUrl: url })\n              if (userInfo.value) {\n                userInfo.value.avatarUrl = url\n                uni.setStorageSync('userInfo', userInfo.value)\n              }\n              uni.showToast({ title: '头像已更新', icon: 'success' })\n              resolve(true)\n            } else {\n              uni.showToast({ title: '上传失败', icon: 'none' })\n              reject(new Error('upload error'))\n            }\n          } catch (err) {\n            uni.showToast({ title: '响应解析失败', icon: 'none' })\n            reject(err)\n          }\n        },\n        fail: (err) => {\n          uni.showToast({ title: '上传失败', icon: 'none' })\n          reject(err)\n        },\n        complete: () => {\n          uni.hideLoading()\n        }\n      })\n    })\n  } catch (e) {\n    // 已提示\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.my-page {\n  background-color: #F5F5F5;\n  min-height: 100vh;\n}\n\n/* --- 顶部区域 --- */\n.header-bg {\n    position: relative;\n    height: 452rpx;\n}\n\n.header-bg-image {\n    position: absolute;\n    top:0;\n    left: 0;\n    width: 100%;\n    height: 452rpx;\n    z-index: 1;\n}\n\n.user-profile-box {\n    position: absolute;\n    /* 使用 calc() 动态计算正确的垂直位置 */\n    top: calc(186rpx + var(--status-bar-height));\n    /* 使用我们确认的左边距 */\n    left: 32rpx;\n    right: 40rpx; /* 增加一个右边距，防止编辑按钮贴边 */\n    z-index: 2;\n    display: flex;\n    align-items: center;\n}\n\n.avatar-container {\n  position: relative;\n}\n\n/* 覆盖在头像上的透明按钮，保持可点击 */\n.avatar-choose-btn {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 54px;\n  height: 54px;\n  background: transparent;\n  border: none;\n  padding: 0;\n  opacity: 0;\n}\n\n.user-text-box {\n    display: flex;\n    flex-direction: column;\n    margin-left: 20rpx;\n    color: #ffffff;\n}\n\n.user-info-container {\n    display: flex;\n    flex-direction: column;\n    align-items: flex-start;\n}\n\n.username-row {\n    display: flex;\n    align-items: center;\n}\n\n.username-text {\n    max-width: 200rpx;\n    height: 52rpx;\n    font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;\n    font-weight: normal;\n    font-size: 36rpx;\n    color: #FFFFFF;\n    line-height: 52rpx;\n    text-align: left;\n    font-style: normal;\n    text-transform: none;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n}\n\n\n\n\n\n.edit-icon-box {\n    display: none;\n}\n\n.edit-icon {\n    width: 28rpx;\n    height: 28rpx;\n    margin-left: 16rpx;\n}\n\n/* --- 报名订单卡片 --- */\n.order-card-container {\n    position: absolute;\n    top: calc(348rpx + var(--status-bar-height));\n    left: 24rpx;\n    right: 24rpx;\n    height: 116rpx;\n    box-sizing: border-box;\n    z-index: 3;\n    margin: 0;\n    border-radius: 16rpx;\n    overflow: hidden;\n}\n\n.order-card-bg-image {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    z-index: 1;\n    border-radius: 16rpx;\n}\n\n.order-card-content {\n    position: relative;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 0 32rpx;\n    height: 100%;\n    z-index: 2;\n}\n\n.order-card-left {\n\tmargin-left:68rpx;\n    display: flex;\n    align-items: center;\n}\n\n.order-card-text {\n    color: #8C5E2D;\n    font-size: 32rpx;\n    font-weight: bold;\n    margin-left: 16rpx;\n}\n\n.order-card-right {\n    display: flex;\n    align-items: center;\n}\n\n.order-card-action-wrapper {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 116rpx;\n    height: 40rpx;\n    border-radius: 20rpx 20rpx 20rpx 20rpx;\n    border: 1rpx solid #E69D3A;\n    background-color: rgba(255, 255, 255, 0.1);\n}\n\n.order-card-action {\n    // width: 48rpx;\n    height: 36rpx;\n    font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;\n    font-weight: normal;\n    font-size: 24rpx;\n    color: #452D03;\n    line-height: 36rpx;\n    text-align: left;\n    font-style: normal;\n    text-transform: none;\n    margin-right: 4rpx;\n}\n\n.order-card-arrow {\n\twidth: 32rpx;\n\theight: 32rpx;\n}\n\n/* --- 白色列表 --- */\n.menu-list-card {\n    background-color: #ffffff;\n    margin: 92rpx 24rpx 0 24rpx;\n    border-radius: 20rpx;\n    padding: 10rpx 0;\n    position: relative;\n    z-index: 2;\n}\n\n.menu-icon {\n    width: 48rpx;\n    height: 48rpx;\n    margin-right: 20rpx;\n}\n\n.logout-button-wrapper {\n  padding: 24rpx 0;\n  margin: 0 24rpx; \n}\n\n.custom-logout-btn {\n  width: 702rpx;\n  height: 76rpx;\n  background: #FFFFFF;\n  border-radius: 8rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 28rpx;\n  color: #333333;\n  \n  &:active {\n    background-color: #f5f5f5;\n  }\n}\n</style>", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages/profile/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "computed", "deleteAccountApi", "updateUserInfoApi", "onShow", "onLoad", "BASE_URL"], "mappings": ";;;;;;;;;;;;;;;;AA6FA,MAAM,eAAe,MAAW;AAIhC,MAAM,sBAAsB;;;;;;AAQ5B,UAAM,aAAaA,cAAG,IAAC,KAAK;AAC5B,UAAM,WAAWA,cAAG,IAAC,IAAI;AAEzB,UAAM,mBAAmBA,cAAG,IAAC,EAAE;AAC/B,UAAM,cAAcA,cAAG,IAAC,EAAE;AAC1B,UAAM,cAAcA,cAAG,IAAC,EAAE;AAC1B,UAAM,gBAAgBA,cAAG,IAAC,EAAE;AAC5B,UAAM,eAAeA,cAAG,IAAC,EAAE;AAC3B,UAAM,kBAAkBA,cAAG,IAAC,EAAE;AAC9B,UAAM,iBAAiBA,cAAG,IAAC,EAAE;AAC7B,UAAM,gBAAgBA,cAAG,IAAC,EAAE;AAC5B,UAAM,iBAAiBA,cAAG,IAAC,EAAE;AAG7B,UAAM,kBAAkB,CAAC,aAAa;AACpC,YAAM,SAASC,cAAAA,MAAI,eAAe,cAAc;AAChD,aAAQ,UAAU,OAAO,QAAQ,IAAK,OAAO,QAAQ,IAAI;AAAA,IAC3D;AAGA,UAAM,uBAAuB,MAAM;AACjC,uBAAiB,QAAQ,gBAAgB,gBAAgB;AAAA,IAC3D;AAGA,UAAM,uBAAuB,MAAM;AACjC,kBAAY,QAAQ,gBAAgB,MAAM;AAC1C,kBAAY,QAAQ,gBAAgB,SAAS;AAC7C,oBAAc,QAAQ,gBAAgB,aAAa;AACnD,mBAAa,QAAQ,gBAAgB,UAAU;AAC/C,sBAAgB,QAAQ,gBAAgB,aAAa;AACrD,qBAAe,QAAQ,gBAAgB,aAAa;AACpD,oBAAc,QAAQ,gBAAgB,WAAW;AACjD,qBAAe,QAAQ,gBAAgB,eAAe;AAAA,IACxD;AAGA,UAAM,qBAAqBC,cAAQ,SAAC,MAAM;AACxC,UAAI,CAAC,SAAS,OAAO;AACnB,eAAO;AAAA,MACR;AAGD,UAAI,SAAS,MAAM,UAAU;AAC3B,eAAO,SAAS,MAAM;AAAA,MACvB;AAED,UAAI,SAAS,MAAM,aAAa;AAE9B,cAAM,QAAQ,SAAS,MAAM;AAC7B,YAAI,MAAM,WAAW,IAAI;AACvB,iBAAO,MAAM,UAAU,GAAG,CAAC,IAAI,SAAS,MAAM,UAAU,CAAC;AAAA,QAC1D;AACD,eAAO;AAAA,MACR;AAED,aAAO;AAAA,IACT,CAAC;AAED,UAAM,kBAAkBA,cAAQ,SAAC,MAAM;;AACrC,UAAI,CAAC,WAAW,OAAO;AACrB,eAAO;AAAA,MACR;AAGD,YAAM,UAAQ,cAAS,UAAT,mBAAgB,YAAS,cAAS,UAAT,mBAAgB;AAEvD,UAAI,CAAC,OAAO;AACV,eAAO;AAAA,MACR;AAGD,UAAI,MAAM,WAAW,IAAI;AACvB,eAAO,MAAM,UAAU,GAAG,CAAC,IAAI,SAAS,MAAM,UAAU,CAAC;AAAA,MAC1D;AAGD,aAAO;AAAA,IACT,CAAC;AAID,UAAM,iBAAiB,CAAC,QAAQ;AAE9B,UAAI,CAAC,WAAW,OAAO;AAErBD,sBAAAA,qDAAY,cAAc;AAC1B,kBAAW;AACX;AAAA,MACD;AAGDA,oBAAAA,MAAY,MAAA,OAAA,kCAAA,cAAc,GAAG;AAC7BA,oBAAAA,MAAI,WAAW;AAAA,QACb;AAAA,QACA,MAAM,CAAC,QAAQ;AACbA,wBAAAA,MAAc,MAAA,SAAA,kCAAA,WAAW,GAAG;AAC5BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,uBAAuB,MAAM;AACjC,UAAI,CAAC,WAAW,OAAO;AACrBA,sBAAAA,MAAA,MAAA,OAAA,kCAAY,gBAAgB;AAC5B,kBAAW;AAAA,MACf,OAAS;AACLA,sBAAAA,MAAA,MAAA,OAAA,kCAAY,kBAAkB;AAC9BA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK;AAAA,UACL,MAAM,CAAC,QAAQ;AACbA,0BAAAA,MAAA,MAAA,SAAA,kCAAc,aAAa,GAAG;AAC9BA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YACpB,CAAS;AAAA,UACF;AAAA,QACP,CAAK;AAAA,MACF;AAAA,IACH;AAIA,UAAM,2BAA2B,MAAM;AACrC,UAAI,CAAC,WAAW,OAAO;AACrBA,sBAAAA,MAAA,MAAA,OAAA,kCAAY,WAAW;AACvB,kBAAW;AACX;AAAA,MACD;AAGDA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEfA,0BAAAA,MAAY,MAAA,OAAA,kCAAA,UAAU;AACtB,iCAAsB;AAAA,UACvB;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,uBAAuB,YAAY;AACvCA,oBAAAA,qDAAY,oBAAoB;AAGhCA,oBAAAA,MAAI,YAAY,EAAE,OAAO,UAAS,CAAE;AAEpC,UAAI;AACFA,sBAAAA,MAAY,MAAA,OAAA,kCAAA,aAAa;AACzB,cAAM,SAAS,MAAME,+BAAkB;AAEvCF,sBAAAA,MAAA,MAAA,OAAA,kCAAY,aAAa,MAAM;AAG/BA,sBAAAA,MAAI,YAAa;AAGjBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAGD,8BAAuB;AAEvBA,sBAAAA,MAAA,MAAA,OAAA,kCAAY,UAAU;AAAA,MAEvB,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,kCAAA,gBAAgB,KAAK;AAGnCA,sBAAAA,MAAI,YAAa;AAGjBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAGDA,sBAAAA,MAAA,MAAA,SAAA,kCAAc,WAAW;AAAA,UACvB,SAAS,MAAM;AAAA,UACf,OAAO,MAAM;AAAA,QACnB,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,mBAAmB,MAAM;AAE7B,YAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AACxC,YAAM,eAAeA,cAAAA,MAAI,eAAe,UAAU;AAElDA,oBAAAA,MAAY,MAAA,OAAA,kCAAA,kBAAkB,KAAK;AACnCA,oBAAAA,MAAA,MAAA,OAAA,kCAAY,qBAAqB,YAAY;AAE7C,YAAM,iBAAiB,CAAC,CAAC;AACzBA,oBAAAA,MAAA,MAAA,OAAA,kCAAY,aAAa,cAAc;AACvCA,oBAAA,MAAA,MAAA,OAAA,kCAAY,aAAa,WAAW,KAAK;AAGzC,iBAAW,QAAQ;AACnB,eAAS,QAAQ,gBAAgB;AAEjC,UAAI,WAAW,SAAS,cAAc;AAEpCA,sBAAAA,MAAA,MAAA,OAAA,kCAAY,eAAe,YAAY;AACvC,YAAI,aAAa,aAAa;AAC5BA,wBAAA,MAAA,MAAA,OAAA,kCAAY,UAAU,aAAa,WAAW;AAAA,QAC/C;AAAA,MACF,WAAU,WAAW,SAAS,CAAC,cAAc;AAC5CA,sBAAAA,qDAAY,cAAc;AAAA,MAC9B,OAAS;AACLA,sBAAAA,MAAY,MAAA,OAAA,kCAAA,OAAO;AAAA,MACpB;AAAA,IACH;AAGA,UAAM,YAAY,MAAM;AACtBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,SAAS,CAAC,kBAAkB,UAAU;AAE1C,UAAI,iBAAiB;AACnB,sBAAc,OAAO;AACrB;AAAA,MACD;AAGDA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,0BAAc,OAAO;AAAA,UACtB;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,qBAAqB,MAAM;;AAC/B,UAAI,CAAC,WAAW,OAAO;AACrBA,sBAAAA,MAAA,MAAA,OAAA,kCAAY,WAAW;AACvB,kBAAW;AACX;AAAA,MACD;AAGDA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,iBAAiB;AAAA,QACjB,WAAS,cAAS,UAAT,mBAAgB,aAAY;AAAA,QACrC,SAAS,OAAO,QAAQ;AACtB,cAAI,IAAI,WAAW,IAAI,WAAW,IAAI,QAAQ,QAAQ;AACpD,kBAAM,cAAc,IAAI,QAAQ,KAAM;AAGtC,gBAAI,YAAY,SAAS,qBAAqB;AAC5CA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO,SAAS,mBAAmB;AAAA,gBACnC,MAAM;AAAA,gBACN,UAAU;AAAA,cACtB,CAAW;AACD;AAAA,YACD;AAGD,kBAAM,eAAe,WAAW;AAAA,UACjC;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,iBAAiB,OAAO,gBAAgB;AAC5CA,oBAAAA,MAAY,MAAA,OAAA,kCAAA,gBAAgB;AAG5B,UAAI,CAAC,eAAe,YAAY,KAAI,EAAG,WAAW,GAAG;AACnDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AACD;AAAA,MACD;AACD,UAAI,YAAY,SAAS,qBAAqB;AAC5CA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,SAAS,mBAAmB;AAAA,UACnC,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AACD;AAAA,MACD;AAGDA,oBAAAA,MAAI,YAAY,EAAE,OAAO,UAAS,CAAE;AAEpC,UAAI;AACFA,sBAAAA,qDAAY,4BAA4B,WAAW;AACnD,cAAM,SAAS,MAAMG,gCAAkB;AAAA,UACrC,UAAU;AAAA,QAChB,CAAK;AAEDH,sBAAAA,MAAY,MAAA,OAAA,kCAAA,eAAe,MAAM;AAGjCA,sBAAAA,MAAI,YAAa;AAGjB,YAAI,SAAS,OAAO;AAClB,mBAAS,MAAM,WAAW;AAE1BA,wBAAAA,MAAI,eAAe,YAAY,SAAS,KAAK;AAAA,QAC9C;AAGDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAEDA,sBAAAA,MAAA,MAAA,OAAA,kCAAY,QAAQ;AAAA,MAErB,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,kCAAA,gBAAgB,KAAK;AAGnCA,sBAAAA,MAAI,YAAa;AAGjBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAEDA,sBAAAA,MAAc,MAAA,SAAA,kCAAA,aAAa;AAAA,UACzB,SAAS,MAAM;AAAA,UACf,OAAO,MAAM;AAAA,QACnB,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,gBAAgB,CAAC,YAAY;AACjCA,oBAAAA,qDAAY,kBAAkB;AAG9BA,oBAAG,MAAC,kBAAkB,OAAO;AAC7BA,oBAAG,MAAC,kBAAkB,UAAU;AAGhC,iBAAW,QAAQ;AACnB,eAAS,QAAQ;AAGjB,UAAI,SAAS;AACXA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAAA,MACF;AAEDA,oBAAAA,MAAY,MAAA,OAAA,kCAAA,eAAe;AAAA,IAC7B;AAGA,UAAM,wBAAwB,MAAM;AAClCA,oBAAAA,qDAAY,kBAAkB;AAG9BA,oBAAG,MAAC,kBAAkB,OAAO;AAC7BA,oBAAG,MAAC,kBAAkB,UAAU;AAGhC,iBAAW,QAAQ;AACnB,eAAS,QAAQ;AAEjBA,oBAAAA,MAAY,MAAA,OAAA,kCAAA,iBAAiB;AAAA,IAC/B;AAKAI,kBAAAA,OAAO,MAAM;AAEXJ,oBAAG,MAAC,WAAU;AAEd,iBAAW,MAAM;AACf,yBAAkB;AAClB,6BAAsB;AACtB,6BAAsB;AAAA,MACvB,GAAE,GAAG;AAAA,IACR,CAAC;AAEDK,kBAAAA,OAAO,MAAM;AACX,uBAAkB;AAClB,2BAAsB;AACtB,2BAAsB;AAAA,IACxB,CAAC;AAGD,UAAM,iBAAiB,CAAC,MAAM;;AAC5B,YAAM,YAAW,4BAAG,WAAH,mBAAW;AAC5B,UAAI,CAAC;AAAU;AACf,mBAAa,QAAQ;AAAA,IACvB;AAGA,UAAM,eAAe,OAAO,aAAa;AACvC,UAAI,CAAC,WAAW,OAAO;AACrB,kBAAW;AACX;AAAA,MACD;AACD,YAAM,QAAQL,cAAAA,MAAI,eAAe,OAAO;AACxC,UAAI,CAAC,OAAO;AACV,kBAAW;AACX;AAAA,MACD;AAEDA,oBAAAA,MAAI,YAAY,EAAE,OAAO,SAAQ,CAAE;AACnC,UAAI;AACF,cAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AACrCA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAKM,aAAQ,WAAG;AAAA,YAChB;AAAA,YACA,MAAM;AAAA,YACN,QAAQ;AAAA,cACN,eAAe,YAAY;AAAA,YAC5B;AAAA,YACD,SAAS,OAAO,QAAQ;AACtB,kBAAI;AACF,sBAAM,OAAO,KAAK,MAAM,IAAI,QAAQ,IAAI;AACxC,oBAAI,IAAI,eAAe,OAAO,KAAK,SAAS,OAAO,KAAK,KAAK;AAC3D,wBAAM,MAAM,KAAK;AACjB,wBAAMH,gCAAkB,EAAE,WAAW,KAAK;AAC1C,sBAAI,SAAS,OAAO;AAClB,6BAAS,MAAM,YAAY;AAC3BH,kCAAAA,MAAI,eAAe,YAAY,SAAS,KAAK;AAAA,kBAC9C;AACDA,gCAAG,MAAC,UAAU,EAAE,OAAO,SAAS,MAAM,WAAW;AACjD,0BAAQ,IAAI;AAAA,gBAC1B,OAAmB;AACLA,gCAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,QAAQ;AAC7C,yBAAO,IAAI,MAAM,cAAc,CAAC;AAAA,gBACjC;AAAA,cACF,SAAQ,KAAK;AACZA,8BAAG,MAAC,UAAU,EAAE,OAAO,UAAU,MAAM,QAAQ;AAC/C,uBAAO,GAAG;AAAA,cACX;AAAA,YACF;AAAA,YACD,MAAM,CAAC,QAAQ;AACbA,4BAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,QAAQ;AAC7C,qBAAO,GAAG;AAAA,YACX;AAAA,YACD,UAAU,MAAM;AACdA,4BAAAA,MAAI,YAAa;AAAA,YAClB;AAAA,UACT,CAAO;AAAA,QACP,CAAK;AAAA,MACF,SAAQ,GAAG;AAAA,MAEX;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9kBA,GAAG,WAAW,eAAe;"}