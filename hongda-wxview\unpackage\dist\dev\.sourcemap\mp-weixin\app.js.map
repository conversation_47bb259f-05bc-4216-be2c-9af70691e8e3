{"version": 3, "file": "app.js", "sources": ["App.vue", "main.js"], "sourcesContent": ["<script>\n// 1. 导入API方法 (保留，用于加载图片等其他资源)\nimport { getAllAssets } from '@/api/platform/asset.js';\n\nexport default {\n  onLaunch: function () {\n    console.log('App Launch');\n\n    // 【优化】分离资源加载和字体加载\n    this.fetchAndCacheAssets(); // 继续负责图片等后台资源\n    this.loadCdnFonts();        // 【新增】专门负责从CDN加载所有字体\n    this.loadIconFont();        // 保留uview-plus图标字体的加载\n  },\n  onShow: function () {\n    console.log('App Show');\n  },\n  onHide: function () {\n    console.log('App Hide');\n  },\n  methods: {\n    /**\n     * 【新增】从CDN加载所有自定义字体\n     * 由于后台系统只能上传文件，我们在此处直接指定稳定高效的jsDelivr CDN地址\n     */\n    loadCdnFonts() {\n      const fontsToLoad = [\n        {\n          family: 'Alibaba PuHuiTi 3.0',\n          // 阿里巴巴普惠体】jsDelivr CDN地址\n          source: 'url(\"https://cdn.jsdelivr.net/gh/xiahezaixiamen/my-fonts/AlibabaPuHuiTi-3-55-Regular.woff2\")'\n        },\n        {\n          family: 'YouSheBiaoTiHei',\n          //优设标题黑】jsDelivr CDN地址\n          source: 'url(\"https://cdn.jsdelivr.net/gh/xiahezaixiamen/my-fonts/YouSheBiaoTiHei.woff2\")'\n        }\n      ];\n\n      let loadedCount = 0;\n      const totalFonts = fontsToLoad.length;\n\n      fontsToLoad.forEach(font => {\n        uni.loadFontFace({\n          global: true,\n          family: font.family,\n          source: font.source,\n          success: () => {\n            console.log(`CDN字体 [${font.family}] 加载成功!`);\n            loadedCount++;\n\n            // 当所有字体都加载完成时，触发全局事件和设置全局状态\n            if (loadedCount === totalFonts) {\n              // 设置全局状态\n              this.globalData = this.globalData || {};\n              this.globalData.fontLoaded = true;\n\n              // 触发全局事件，通知所有组件字体已加载完成\n              uni.$emit('customFontsLoaded');\n              console.log('所有自定义字体加载完成，已触发全局事件');\n            }\n          },\n          fail(err) {\n            console.error(`CDN字体 [${font.family}] 加载失败:`, err);\n            loadedCount++;\n\n            // 即使有字体加载失败，也要检查是否所有字体都处理完了\n            if (loadedCount === totalFonts) {\n              this.globalData = this.globalData || {};\n              this.globalData.fontLoaded = true;\n              uni.$emit('customFontsLoaded');\n              console.log('字体加载处理完成（部分可能失败），已触发全局事件');\n            }\n          }\n        });\n      });\n    },\n\n    /**\n     * 【保留并优化】获取、缓存静态资源（不再负责字体）\n     * 此函数继续为App中的图片、背景等资源服务\n     */\n    async fetchAndCacheAssets() {\n      try {\n        const response = await getAllAssets();\n        if (response.code === 200 && Array.isArray(response.data)) {\n          const assetMap = response.data.reduce((map, item) => {\n            if (item.assetKey && item.assetUrl) {\n              map[item.assetKey] = item.assetUrl;\n            }\n            return map;\n          }, {});\n\n          uni.setStorageSync('staticAssets', assetMap);\n          console.log('小程序图片等静态资源已更新并缓存成功！');\n          // 【已移除】不再从此函数中调用旧的字体加载方法\n        }\n      } catch (error) {\n        console.error('获取小程序静态资源失败', error);\n      }\n    },\n\n    /**\n     * 【已废弃】loadDynamicFonts 方法可以安全删除了，其功能已被 loadCdnFonts 替代\n     */\n    \n    /**\n     * 加载uView图标字体 (保留)\n     */\n    loadIconFont() {\n      // #ifdef APP || H5 || MP-WEIXIN || MP-ALIPAY\n      uni.loadFontFace({\n        global: true,\n        family: 'uicon-iconfont',\n        source: 'url(\"https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf\")',\n        success() {\n          console.log('uview-plus图标字体加载成功');\n        },\n        fail(err) {\n          console.error('uview-plus图标字体加载失败:', err);\n          uni.loadFontFace({\n            global: true,\n            family: 'uicon-iconfont',\n            source: 'url(\"https://cdn.jsdelivr.net/npm/uview-plus@3.1.37/fonts/uicon-iconfont.ttf\")',\n            success() {\n              console.log('备用图标字体加载成功');\n            },\n            fail(err2) {\n              console.error('备用图标字体也加载失败:', err2);\n            }\n          });\n        }\n      });\n      // #endif\n    },\n  }\n}\n</script>\n\n<style lang=\"scss\">\n/* 每个页面公共css */\n@import \"@/uni_modules/uview-plus/index.scss\";\n\n/* 防止页面跳转时内容残留 */\nuni-page-wrapper {\n  overflow: hidden !important;\n}\nuni-page-body {\n  overflow: hidden !important;\n}\n\n/* 【修改】更新全局字体栈，将新字体作为最高优先级 */\nbody, page, view, text {\n  font-family: 'Alibaba PuHuiTi 3.0', sans-serif;\n}\n</style>\n", "import App from './App'\r\n\r\n// #ifndef VUE3\r\nimport Vue from 'vue'\r\nimport './uni.promisify.adaptor'\r\nVue.config.productionTip = false\r\nApp.mpType = 'app'\r\nconst app = new Vue({\r\n\t...App\r\n})\r\napp.$mount()\r\n// #endif\r\n\r\n// #ifdef VUE3\r\nimport { createSSRApp } from 'vue'\r\nimport uviewPlus from '@/uni_modules/uview-plus'\r\n\r\nexport function createApp() {\r\n\tconst app = createSSRApp(App)\r\n\r\n\t// 保留您原有的 uview-plus 插件配置\r\n\tapp.use(uviewPlus, () => {\r\n\t\treturn {\r\n\t\t\toptions: {\r\n\t\t\t\tconfig: {\r\n\t\t\t\t\tunit: 'rpx'\r\n\t\t\t\t},\r\n\t\t\t\tprops: {\r\n\t\t\t\t\t// ...\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t})\r\n\r\n\treturn {\r\n\t\tapp\r\n\t}\r\n}\r\n// #endif"], "names": ["uni", "getAllAssets", "createSSRApp", "App", "uviewPlus"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAIA,MAAK,YAAU;AAAA,EACb,UAAU,WAAY;AACpBA,kBAAAA,MAAY,MAAA,OAAA,gBAAA,YAAY;AAGxB,SAAK,oBAAmB;AACxB,SAAK,aAAY;AACjB,SAAK,aAAY;AAAA,EAClB;AAAA,EACD,QAAQ,WAAY;AAClBA,kBAAAA,MAAA,MAAA,OAAA,iBAAY,UAAU;AAAA,EACvB;AAAA,EACD,QAAQ,WAAY;AAClBA,kBAAAA,MAAA,MAAA,OAAA,iBAAY,UAAU;AAAA,EACvB;AAAA,EACD,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,IAKP,eAAe;AACb,YAAM,cAAc;AAAA,QAClB;AAAA,UACE,QAAQ;AAAA;AAAA,UAER,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,QAAQ;AAAA;AAAA,UAER,QAAQ;AAAA,QACV;AAAA;AAGF,UAAI,cAAc;AAClB,YAAM,aAAa,YAAY;AAE/B,kBAAY,QAAQ,UAAQ;AAC1BA,sBAAAA,MAAI,aAAa;AAAA,UACf,QAAQ;AAAA,UACR,QAAQ,KAAK;AAAA,UACb,QAAQ,KAAK;AAAA,UACb,SAAS,MAAM;AACbA,gCAAA,MAAA,OAAA,iBAAY,UAAU,KAAK,MAAM,SAAS;AAC1C;AAGA,gBAAI,gBAAgB,YAAY;AAE9B,mBAAK,aAAa,KAAK,cAAc,CAAA;AACrC,mBAAK,WAAW,aAAa;AAG7BA,kCAAI,MAAM,mBAAmB;AAC7BA,4BAAAA,MAAY,MAAA,OAAA,iBAAA,qBAAqB;AAAA,YACnC;AAAA,UACD;AAAA,UACD,KAAK,KAAK;AACRA,0BAAAA,sCAAc,UAAU,KAAK,MAAM,WAAW,GAAG;AACjD;AAGA,gBAAI,gBAAgB,YAAY;AAC9B,mBAAK,aAAa,KAAK,cAAc,CAAA;AACrC,mBAAK,WAAW,aAAa;AAC7BA,kCAAI,MAAM,mBAAmB;AAC7BA,4BAAAA,MAAA,MAAA,OAAA,iBAAY,0BAA0B;AAAA,YACxC;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,IAMD,MAAM,sBAAsB;AAC1B,UAAI;AACF,cAAM,WAAW,MAAMC,mBAAAA;AACvB,YAAI,SAAS,SAAS,OAAO,MAAM,QAAQ,SAAS,IAAI,GAAG;AACzD,gBAAM,WAAW,SAAS,KAAK,OAAO,CAAC,KAAK,SAAS;AACnD,gBAAI,KAAK,YAAY,KAAK,UAAU;AAClC,kBAAI,KAAK,QAAQ,IAAI,KAAK;AAAA,YAC5B;AACA,mBAAO;AAAA,UACR,GAAE,CAAE,CAAA;AAELD,wBAAAA,MAAI,eAAe,gBAAgB,QAAQ;AAC3CA,wBAAAA,MAAY,MAAA,OAAA,iBAAA,qBAAqB;AAAA,QAEnC;AAAA,MACA,SAAO,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,iBAAc,eAAe,KAAK;AAAA,MACpC;AAAA,IACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASD,eAAe;AAEbA,oBAAAA,MAAI,aAAa;AAAA,QACf,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,UAAU;AACRA,wBAAAA,MAAA,MAAA,OAAA,kBAAY,oBAAoB;AAAA,QACjC;AAAA,QACD,KAAK,KAAK;AACRA,wBAAA,MAAA,MAAA,SAAA,kBAAc,uBAAuB,GAAG;AACxCA,wBAAAA,MAAI,aAAa;AAAA,YACf,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,UAAU;AACRA,4BAAAA,MAAY,MAAA,OAAA,kBAAA,YAAY;AAAA,YACzB;AAAA,YACD,KAAK,MAAM;AACTA,4BAAA,MAAA,MAAA,SAAA,kBAAc,gBAAgB,IAAI;AAAA,YACpC;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IAEF;AAAA,EACH;AACF;ACtHO,SAAS,YAAY;AAC3B,QAAM,MAAME,cAAY,aAACC,SAAG;AAG5B,MAAI,IAAIC,4BAAAA,WAAW,MAAM;AACxB,WAAO;AAAA,MACN,SAAS;AAAA,QACR,QAAQ;AAAA,UACP,MAAM;AAAA,QACN;AAAA,QACD,OAAO;AAAA;AAAA,QAEN;AAAA,MACD;AAAA,IACD;AAAA,EACH,CAAE;AAED,SAAO;AAAA,IACN;AAAA,EACA;AACF;;;"}