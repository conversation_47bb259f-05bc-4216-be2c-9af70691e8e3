<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="页面标题" prop="title">
        <el-input
            v-model="queryParams.title"
            placeholder="请输入页面标题"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="页面类型" prop="pageType">
        <el-select v-model="queryParams.pageType" placeholder="请选择页面类型" style="width: 160px;" clearable>
          <el-option
              v-for="dict in hongda_page_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" style="width: 130px" clearable>
          <el-option
              v-for="dict in hongda_common_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['platform:page:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['platform:page:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['platform:page:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['platform:page:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="pageList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="页面ID" align="center" prop="id" width="80px" />
      <el-table-column label="页面标题" align="center" prop="title" :show-overflow-tooltip="true" />
      <el-table-column label="页面类型" align="center" prop="pageType">
        <template #default="scope">
          <dict-tag :options="hongda_page_type" :value="scope.row.pageType"/>
        </template>
      </el-table-column>
      <el-table-column label="目标/内容" align="center" prop="targetUrl" :show-overflow-tooltip="true">
        <template #default="scope">
          <span v-if="scope.row.pageType === 'RICH_TEXT' || scope.row.pageType === 'CUSTOM_PAGE'">[自定义页面内容]</span>
          <span v-else>{{ scope.row.targetUrl }}</span>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="sortOrder" />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="hongda_common_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="140px">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['platform:page:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['platform:page:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <el-dialog :title="title" v-model="open" width="600px" append-to-body :close-on-click-modal="false">
      <el-form ref="pageRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="页面标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入页面标题" />
        </el-form-item>
        <el-form-item label="页面类型" prop="pageType">
          <el-select v-model="form.pageType" placeholder="请选择页面类型">
            <el-option
                v-for="dict in hongda_page_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item v-if="form.pageType === 'CUSTOM_PAGE' || form.pageType === 'RICH_TEXT'" label="页面内容" prop="content">
          <el-button type="primary" icon="Edit" @click="editorOpen = true">
            打开内容编辑器
          </el-button>
          <div style="margin-top: 8px; color: #909399; font-size: 13px;">
            {{ form.content ? '✔️ 已设置内容' : '❌ 未设置内容' }}
            <span v-if="form.content" style="margin-left: 12px;">
              字数: {{ form.content.replace(/<[^>]*>/g, '').length }}
            </span>
          </div>
        </el-form-item>

        <el-form-item
            v-if="form.pageType === 'INTERNAL_LINK' || form.pageType === 'EXTERNAL_LINK'"
            label="目标链接"
            prop="targetUrl"
        >
          <el-input v-model="form.targetUrl" type="textarea" placeholder="请输入小程序内部路径或完整的网址" />
        </el-form-item>

        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="form.sortOrder" controls-position="right" :min="0" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
                v-for="dict in hongda_common_status"
                :key="dict.value"
                :label="parseInt(dict.value)"
            >{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <el-drawer
        v-model="editorOpen"
        :with-header="false"
        direction="rtl"
        size="75%"
        :destroy-on-close="true"
        :close-on-click-modal="false"
    >
      <div class="editor-drawer-container">
        <div class="editor-drawer-header">
          <h3>内容编辑器</h3>
          <p>您正在编辑页面: {{ form.title || '新页面' }}</p>
        </div>
        <div class="editor-drawer-body">
          <editor v-model="form.content" height="100%" />
        </div>
        <div class="editor-drawer-footer">
          <div class="footer-left">
            <el-icon><Document /></el-icon>
            <span>{{ form.content ? '内容已保存' : '尚未保存内容' }}</span>
            <el-divider direction="vertical" />
            <span>字数: {{ form.content ? form.content.replace(/<[^>]*>/g, '').length : 0 }}</span>
          </div>
          <div class="footer-right">
            <el-button @click="editorOpen = false">
              <el-icon><Close /></el-icon>
              取 消
            </el-button>
            <el-button type="primary" @click="editorOpen = false">
              <el-icon><Check /></el-icon>
              完 成
            </el-button>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup name="Page">
import { listPage, getPage, delPage, addPage, updatePage } from "@/api/platform/page";
import { computed, watch } from "vue";
// [新增] 导入所需图标
import { Document, Close, Check } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();
const { hongda_page_type, hongda_common_status } = proxy.useDict('hongda_page_type', 'hongda_common_status');

const pageList = ref([]);
const open = ref(false);
const editorOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    title: null,
    pageType: null,
    status: null,
  },
});

const { queryParams, form } = toRefs(data);

watch(() => form.value.pageType, (newType) => {
  if (newType === 'RICH_TEXT' || newType === 'CUSTOM_PAGE') {
    form.value.targetUrl = null;
  } else if (newType === 'INTERNAL_LINK' || newType === 'EXTERNAL_LINK') {
    form.value.content = null;
  }
});

const rules = computed(() => {
  const baseRules = {
    title: [{ required: true, message: "页面标题不能为空", trigger: "blur" }],
    pageType: [{ required: true, message: "页面类型不能为空", trigger: "change" }],
    status: [{ required: true, message: "状态不能为空", trigger: "change" }],
  };

  if (form.value.pageType === 'RICH_TEXT' || form.value.pageType === 'CUSTOM_PAGE') {
    // [优化] 内容不再是必填项，因为在抽屉中编辑，主表单不直接校验
    // baseRules.content = [{ required: true, message: "页面内容不能为空" }];
  } else if (form.value.pageType === 'INTERNAL_LINK' || form.value.pageType === 'EXTERNAL_LINK') {
    baseRules.targetUrl = [{ required: true, message: "目标链接不能为空", trigger: "blur" }];
  }
  return baseRules;
});

function getList() {
  loading.value = true;
  listPage(queryParams.value).then(response => {
    pageList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

function cancel() {
  reset();
  open.value = false;
}

// platform/page/index.vue

function reset() {
  form.value = {
    id: null,
    title: null,
    pageType: 'RICH_TEXT', // [关键修改] 改为与数据库字典一致的正确值
    content: null,
    targetUrl: null,
    sortOrder: 0,
    status: 1,
    remark: null,
  };
  proxy.resetForm("pageRef");
}

function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加页面";
}

function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getPage(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改页面";
  });
}

function submitForm() {
  proxy.$refs["pageRef"].validate(valid => {
    if (valid) {
      const apiCall = form.value.id != null ? updatePage(form.value) : addPage(form.value);
      apiCall.then(() => {
        proxy.$modal.msgSuccess(form.value.id != null ? "修改成功" : "新增成功");
        open.value = false;
        getList();
      });
    }
  });
}

function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除页面管理编号为"' + _ids + '"的数据项？').then(function() {
    return delPage(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

function handleExport() {
  proxy.download('platform/page/export', {
    ...queryParams.value
  }, `page_${new Date().getTime()}.xlsx`);
}

getList();
</script>

<style scoped>
.editor-drawer-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--el-bg-color-page);
}

.editor-drawer-header {
  padding: 24px 32px 20px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  position: relative;
  z-index: 10;
}

.editor-drawer-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  display: flex;
  align-items: center;
  gap: 8px;
}

.editor-drawer-header h3::before {
  content: "📝";
  font-size: 18px;
}

.editor-drawer-header p {
  margin: 12px 0 0;
  font-size: 14px;
  color: var(--el-text-color-secondary);
  font-weight: 400;
  line-height: 1.5;
}

.editor-drawer-body {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.editor-drawer-body :deep(.w-e-container) {
  width: 100% !important;
  height: 100% !important;
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  background: #fff;
  display: flex;
  flex-direction: column;
}

.editor-drawer-body :deep(.w-e-toolbar) {
  border-bottom: 1px solid var(--el-border-color-lighter);
  background: var(--el-bg-color);
  padding: 12px 16px;
  border-radius: 8px 8px 0 0;
  min-height: auto;
}

.editor-drawer-body :deep(.w-e-toolbar-item) {
  margin: 0 2px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.editor-drawer-body :deep(.w-e-toolbar-item:hover) {
  background-color: var(--el-fill-color-light);
}

.editor-drawer-body :deep(.w-e-text-container) {
  flex: 1;
  background: #fff;
  border-radius: 0 0 8px 8px;
}

.editor-drawer-body :deep(.w-e-text) {
  padding: 20px 24px !important;
  font-size: 15px;
  line-height: 1.8;
  color: var(--el-text-color-primary);
  min-height: 400px;
}

.editor-drawer-body :deep(.w-e-text:focus) {
  outline: none;
  box-shadow: inset 0 0 0 2px var(--el-color-primary-light-7);
}

.editor-drawer-footer {
  padding: 20px 32px 24px;
  background: var(--el-bg-color);
  border-top: 1px solid var(--el-border-color-light);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 10;
}

.editor-drawer-footer .footer-left {
  display: flex;
  align-items: center;
  gap: 12px;
  color: var(--el-text-color-secondary);
  font-size: 13px;
}

.editor-drawer-footer .footer-right {
  display: flex;
  gap: 12px;
}

.editor-drawer-footer .el-button {
  padding: 10px 24px;
  font-weight: 500;
}
</style>