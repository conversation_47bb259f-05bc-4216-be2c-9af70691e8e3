<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="页面标题" prop="title">
        <el-input
            v-model="queryParams.title"
            placeholder="请输入页面标题"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="页面类型" prop="pageType">
        <el-select v-model="queryParams.pageType" placeholder="请选择页面类型" style="width: 160px;" clearable>
          <el-option
              v-for="dict in hongda_page_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" style="width: 130px" clearable>
          <el-option
              v-for="dict in hongda_common_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['platform:page:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['platform:page:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['platform:page:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['platform:page:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="pageList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="页面ID" align="center" prop="id" width="80px" />
      <el-table-column label="页面标题" align="center" prop="title" :show-overflow-tooltip="true" />
      <el-table-column label="页面类型" align="center" prop="pageType">
        <template #default="scope">
          <dict-tag :options="hongda_page_type" :value="scope.row.pageType"/>
        </template>
      </el-table-column>
      <el-table-column label="目标/内容" align="center" prop="targetUrl" :show-overflow-tooltip="true">
        <template #default="scope">
          <span v-if="scope.row.pageType === 'RICH_TEXT'">[富文本内容]</span>
          <span v-else>{{ scope.row.targetUrl }}</span>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="sortOrder" />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="hongda_common_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="140px">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['platform:page:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['platform:page:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <el-dialog :title="title" v-model="open" width="800px" append-to-body @closed="handleDialogClosed">
      <el-form ref="pageRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="页面标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入页面标题" />
        </el-form-item>
        <el-form-item label="页面类型" prop="pageType">
          <el-select v-model="form.pageType" placeholder="请选择页面类型">
            <el-option
                v-for="dict in hongda_page_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item v-if="form.pageType === 'RICH_TEXT'" label="页面内容" prop="content">
          <editor v-model="form.content" :min-height="192"/>
        </el-form-item>

        <el-form-item
            v-if="form.pageType === 'INTERNAL_LINK' || form.pageType === 'EXTERNAL_LINK'"
            label="目标链接"
            prop="targetUrl"
        >
          <el-input v-model="form.targetUrl" type="textarea" placeholder="请输入小程序内部路径或完整的网址" />
        </el-form-item>

        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="form.sortOrder" controls-position="right" :min="0" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
                v-for="dict in hongda_common_status"
                :key="dict.value"
                :value="parseInt(dict.value)"
            >{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="submitLoading">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Page">
import { listPage, getPage, delPage, addPage, updatePage } from "@/api/platform/page";
import { computed, watch, nextTick } from "vue";

const { proxy } = getCurrentInstance();
const { hongda_page_type, hongda_common_status } = proxy.useDict('hongda_page_type', 'hongda_common_status');

const pageList = ref([]);
const open = ref(false);
const loading = ref(true);
const submitLoading = ref(false); // 新增：提交按钮loading状态
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    title: null,
    pageType: null,
    status: null,
  },
});

const { queryParams, form } = toRefs(data);

// 修复：侦听器改为深度监听，防止初始化时的不必要触发
watch(() => form.value.pageType, (newType, oldType) => {
  // 只有在类型确实改变时才清空字段
  if (oldType && newType !== oldType) {
    if (newType === 'RICH_TEXT') {
      form.value.targetUrl = null;
    } else if (newType === 'INTERNAL_LINK' || newType === 'EXTERNAL_LINK') {
      form.value.content = null;
    }

    // 清除对应字段的验证错误
    nextTick(() => {
      if (proxy.$refs.pageRef) {
        if (newType === 'RICH_TEXT') {
          proxy.$refs.pageRef.clearValidate('targetUrl');
        } else {
          proxy.$refs.pageRef.clearValidate('content');
        }
      }
    });
  }
});

// 计算属性：动态生成校验规则
const rules = computed(() => {
  const baseRules = {
    title: [{ required: true, message: "页面标题不能为空", trigger: "blur" }],
    pageType: [{ required: true, message: "页面类型不能为空", trigger: "change" }],
    status: [{ required: true, message: "状态不能为空", trigger: "change" }],
  };

  if (form.value.pageType === 'RICH_TEXT') {
    baseRules.content = [{ required: true, message: "页面内容不能为空", trigger: "blur" }];
  } else if (form.value.pageType === 'INTERNAL_LINK' || form.value.pageType === 'EXTERNAL_LINK') {
    baseRules.targetUrl = [{ required: true, message: "目标链接不能为空", trigger: "blur" }];
  }

  return baseRules;
});

/** 查询页面管理列表 */
function getList() {
  loading.value = true;
  listPage(queryParams.value).then(response => {
    pageList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  });
}

// 修复：对话框关闭时的处理
function handleDialogClosed() {
  // 对话框完全关闭后再重置表单，避免动画期间的状态冲突
  reset();
}

// 取消按钮
function cancel() {
  open.value = false;
  // 不在这里重置表单，改为在对话框关闭事件中重置
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    title: null,
    pageType: 'RICH_TEXT',
    content: null,
    targetUrl: null,
    sortOrder: 0,
    status: 1,
    remark: null,
  };

  // 确保refs存在后再重置
  nextTick(() => {
    if (proxy.$refs.pageRef) {
      proxy.$refs.pageRef.resetFields();
      proxy.$refs.pageRef.clearValidate();
    }
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = selection.length === 0;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加页面";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row?.id || ids.value[0]; // 修复：确保获取正确的ID

  if (!_id) {
    proxy.$modal.msgError("请选择要修改的数据");
    return;
  }

  loading.value = true;
  getPage(_id).then(response => {
    // 深拷贝数据，避免直接引用导致的问题
    form.value = { ...response.data };
    open.value = true;
    title.value = "修改页面";
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  });
}

/** 提交按钮 */
function submitForm() {
  if (!proxy.$refs.pageRef) return;

  proxy.$refs.pageRef.validate(valid => {
    if (valid) {
      submitLoading.value = true;

      // 创建提交数据的副本，避免直接修改form
      const submitData = { ...form.value };

      const apiCall = submitData.id != null
          ? updatePage(submitData)
          : addPage(submitData);

      apiCall.then(response => {
        proxy.$modal.msgSuccess(submitData.id != null ? "修改成功" : "新增成功");
        open.value = false;
        getList();
      }).catch(error => {
        console.error('提交失败:', error);
      }).finally(() => {
        submitLoading.value = false;
      });
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row?.id || ids.value;

  if (!_ids || (Array.isArray(_ids) && _ids.length === 0)) {
    proxy.$modal.msgError("请选择要删除的数据");
    return;
  }

  const idsStr = Array.isArray(_ids) ? _ids.join(',') : _ids.toString();

  proxy.$modal.confirm(`是否确认删除页面管理编号为"${idsStr}"的数据项？`).then(() => {
    return delPage(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
    // 用户取消删除或删除失败
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('platform/page/export', {
    ...queryParams.value
  }, `page_${new Date().getTime()}.xlsx`);
}

getList();
</script>