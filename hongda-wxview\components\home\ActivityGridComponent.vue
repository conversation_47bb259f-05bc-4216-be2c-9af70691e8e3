<template>
	<view class="activity-section section-wrapper">
        <view class="section-header">
            <view class="title-wrapper">
                <!-- 【修改】直接动态绑定 style 属性，确保字体在加载后能被正确应用 -->
                <text class="title-main" :style="fontStyle">{{ titleParts.main }}</text>
                <text class="title-gradient" :style="fontStyle">{{ titleParts.gradient }}</text>
            </view>
			<view class="section-more" @click="goToEventList">
				<text>更多</text>
				<up-icon name="arrow-right" size="14"></up-icon>
			</view>
		</view>
		
		<view v-if="isLoading" class="loading-state">
			<up-loading-icon mode="spinner" size="40"></up-loading-icon>
			<text class="loading-text">加载中...</text>
		</view>
		
		<view v-else class="activity-grid">
			<view 
				class="activity-card" 
				v-for="(item, index) in activityList" 
				:key="item.id || index"
				@click="goToEventDetail(item)"
			>
				<view class="image-wrapper">
					<image 
						class="activity-image" 
						:src="item.image" 
						mode="aspectFill"
						:lazy-load="true"
					></image>
                    <view class="status-tag" :class="item.statusClass">{{ item.status }}</view>
				</view>
				<view class="activity-info">
					<text class="activity-title">{{ item.title }}</text>
					
					<!-- 时间和地点信息行 -->
					<view class="activity-meta-row">
                        <view class="time-info">
                            <image class="meta-icon" :src="icons.time" mode="widthFix" />
                            <text class="time-text">{{ item.dateTime }}</text>
                            <text class="weekday-text">{{ item.weekday }}</text>
                        </view>
                        <view class="location-info">
                            <image class="meta-icon" :src="icons.location" mode="widthFix" />
                            <text class="location-text">{{ item.location }}</text>
                        </view>
					</view>
					
                    <!-- 剩余名额标签 -->
					<view class="remaining-spots-tag">
						<text class="spots-label">剩余名额：</text>
						<text class="spots-count">{{ item.remainingSpots }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 空状态 -->
		<view v-if="!isLoading && activityList.length === 0" class="empty-state">
			<up-empty mode="data" text="暂无精选活动" textColor="#909399" iconSize="80"></up-empty>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { getHotEventListApi } from '@/api/data/event.js';
import { getFullImageUrl } from '@/utils/image.js';
import { formatEventStatus, getStatusClass, calculateRemainingSpots } from '@/utils/tools.js';

// --- 【新增】字体加载状态 ---
const fontReady = ref(false);

// --- 【新增】动态计算字体样式 ---
const fontStyle = computed(() => {
  if (fontReady.value) {
    // 字体准备好后，返回包含 font-family 的 style 对象
    return { fontFamily: "'YouSheBiaoTiHei', sans-serif" };
  }
  // 否则返回空对象
  return {};
});

// --- 【新增】在组件挂载时检查字体状态 ---
onMounted(() => {
  const app = getApp();
  if (app && app.globalData && app.globalData.fontLoaded) {
    // 如果字体已加载，直接应用样式
    fontReady.value = true;
  } else {
    // 否则，监听全局事件
    uni.$on('customFontsLoaded', onFontsLoaded);
  }
  
  // 原有的 onMounted 逻辑
  try {
    const assets = uni.getStorageSync('staticAssets')
    icons.time = assets?.detail_icon_time || ''
    icons.location = assets?.detail_icon_location || ''
  } catch (e) {}
  fetchHotEvents();
});

// --- 【新增】事件回调函数 ---
const onFontsLoaded = () => {
  fontReady.value = true;
};

// --- 【新增】组件卸载时移除监听 ---
onUnmounted(() => {
  uni.$off('customFontsLoaded', onFontsLoaded);
});


// --- 原有代码 (保持不变) ---
const activityList = ref([]);
const isLoading = ref(false);

const titleParts = computed(() => {
  const title = '精选活动';
  // 【重要】确保标题能被正确分割
  return { main: title.slice(0, 2), gradient: title.slice(2) };
});

const icons = {
  time: '',
  location: ''
};

const parseSafeDate = (input) => {
  if (!input) return null;
  if (input instanceof Date) {
    return isNaN(input.getTime()) ? null : input;
  }
  if (typeof input === 'number') {
    const d = new Date(input);
    return isNaN(d.getTime()) ? null : d;
  }
  if (typeof input === 'string') {
    let s = input.trim();
    if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(s)) {
      s = s.replace(' ', 'T');
    }
    let d = new Date(s);
    if (isNaN(d.getTime())) {
      const m = s.match(/^(\d{4})-(\d{1,2})-(\d{1,2})(?:[ T](\d{1,2}):(\d{2})(?::(\d{2}))?)?/);
      if (m) {
        const y = m[1];
        const mo = m[2];
        const day = m[3];
        const rest = m[4] ? ` ${m[4]}:${m[5]}:${m[6] || '00'}` : '';
        d = new Date(`${y}/${mo}/${day}${rest}`);
      }
    }
    return isNaN(d.getTime()) ? null : d;
  }
  return null;
};

const formatActivityDate = (dateString) => {
  if (!dateString) return '';
  const date = parseSafeDate(dateString);
  if (!date) return '';
  const month = date.getMonth() + 1;
  const day = String(date.getDate()).padStart(2, '0');
  return `${month}.${day}`;
};

const getWeekday = (dateString) => {
  if (!dateString) return '';
  const date = parseSafeDate(dateString);
  if (!date) return '';
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  return weekdays[date.getDay()];
};

const calculateRemaining = (maxParticipants, registeredCount) => {
  if (!maxParticipants || maxParticipants <= 0) {
    return '不限';
  }
  const remaining = maxParticipants - (registeredCount || 0);
  return remaining > 0 ? remaining : 0;
};

const formatActivityLocation = (item) => {
  if (item.city && item.city.trim()) {
    return item.city.trim();
  }
  return '待定';
};

const fetchHotEvents = async () => {
  if (isLoading.value) return;
  isLoading.value = true;
  try {
    const response = await getHotEventListApi(8);
    const { rows = [] } = response;
    const registeringRows = rows.filter(item => getStatusClass(item.status) !== 'ended').slice(0, 4);
    activityList.value = registeringRows.map(item => ({
      id: item.id,
      title: item.title,
      image: getFullImageUrl(item.coverImageUrl),
      location: formatActivityLocation(item),
      status: formatEventStatus(item.status),
      statusClass: getStatusClass(item.status) === 'ended' ? 'status-ended' : 'status-registering',
      dateTime: formatActivityDate(item.startTime),
      weekday: getWeekday(item.startTime),
      remainingSpots: calculateRemaining(item.maxParticipants, item.registeredCount)
    }));
  } catch (error) {
    console.error('获取热门活动失败:', error);
    activityList.value = [
      { 
        id: 1, 
        title: '2025 Ozen卖家增长峰会·北京站', 
        status: '报名中', 
        statusClass: 'status-registering', 
        location: '北京', 
        image: 'https://via.placeholder.com/170x100/3c9cff/fff?text=活动1',
        dateTime: '7.15',
        weekday: '周二',
        remainingSpots: '29'
      },
    ];
    uni.showToast({
      title: '获取活动数据失败，显示示例数据',
      icon: 'none',
      duration: 2000
    });
  } finally {
    isLoading.value = false;
  }
};

const goToEventDetail = (activity) => {
  uni.navigateTo({
    url: `/pages_sub/pages_event/detail?id=${activity.id}`
  });
};

const goToEventList = () => {
  uni.switchTab({
    url: '/pages/event/index',
    fail: () => {
      uni.navigateTo({ url: '/pages/event/index' });
    }
  });
};
</script>

<style lang="scss" scoped>
.section-wrapper {
	margin: 24rpx;
	padding: 20rpx;
	background-color: #ffffff;
	border-radius: 16rpx;
}
.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-bottom: 20rpx;
}
.title-wrapper {
    display: flex;
    align-items: center;
    /* 【修改】移除这里的 font-family，让 JS 动态 style 来控制 */
}
.title-main {
    /* “精选”的样式 */
    font-size: 40rpx;
    font-weight: 400; /* 优设标题黑通常只有一个字重 */
    color: #023F98;
}
.title-gradient {
    /* “活动”的样式 */
    font-size: 40rpx;
    font-weight: 400;
    /* 应用您指定的精确渐变色 */
    background-image: linear-gradient(91.61deg, #FFAD22 0%, #FFBB87 100%);
    /* 关键：实现文字渐变效果 */
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}
.section-more {
	display: flex;
	align-items: center;
	font-size: 28rpx;
	color: #909399;
	cursor: pointer;
	transition: color 0.2s ease;
	
	&:active {
		color: #f56c6c;
	}
}

// 加载状态样式
.loading-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60rpx 0;
	
	.loading-text {
		font-size: 28rpx;
		color: #909399;
		margin-top: 20rpx;
	}
}

// 空状态样式
.empty-state {
	padding: 60rpx 0;
	text-align: center;
}

.activity-grid {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
}
.activity-card {
	width: calc(50% - 10rpx);
	background-color: #ffffff;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
	box-shadow: none;
	transition: transform 0.2s ease, box-shadow 0.2s ease;

	// 点击效果
	&:active {
		transform: scale(0.98);
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.image-wrapper {
		position: relative;
		width: 336rpx;
		height: 192rpx; 
		margin: 0 auto; 
		border-radius: 16rpx; 
		overflow: hidden;
	}

	.activity-image {
		width: 100%;
		height: 100%;
		border-radius: 16rpx;
	}

	.activity-info {
		padding: 16rpx;
	}

	.activity-title {
		width: 100%;
		height: 80rpx;
		font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;
		font-weight: normal;
		font-size: 28rpx;
		color: #23232A;
		text-align: justify;
		font-style: normal;
		text-transform: none;
		line-height: 40rpx; 
		margin-bottom: 12rpx;
		word-break: break-word;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		line-clamp: 2; 
		-webkit-line-clamp: 2;
		overflow: hidden;
		text-overflow: ellipsis;
	}

    // 时间和地点信息行
    .activity-meta-row {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 46rpx; 
        margin-bottom: 12rpx;
		
        .time-info {
            display: flex;
            align-items: center;
            flex: none;
			
			.meta-icon {
				width: 24rpx;
				height: 24rpx;
			}
			
            .time-text {
				width: 66rpx;
				height: 32rpx;
				font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;
				font-weight: normal;
				font-size: 22rpx;
				color: #9B9A9A;
				text-align: left;
				font-style: normal;
				text-transform: none;
				line-height: 32rpx;
                margin-left: 6rpx;
                margin-right: 6rpx; 
			}
			
			.weekday-text {
				font-size: 22rpx;
				color: #9B9A9A;
				line-height: 32rpx;
			}
		}
		
        .location-info {
            display: flex;
            align-items: center;
            flex: none; 
			
			.meta-icon {
				width: 24rpx;
				height: 24rpx;
			}
			
			.location-text {
				width: 66rpx;
				height: 32rpx;
				font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;
				font-weight: normal;
				font-size: 22rpx;
				color: #9B9A9A;
				text-align: left;
				font-style: normal;
				text-transform: none;
				line-height: 32rpx;
				margin-left: 8rpx;
			}
		}
	}

    // 剩余名额
	.remaining-spots-tag {
        display: inline-flex;
        align-items: center;
        justify-content: flex-start;
        height: 40rpx;
        border-radius: 4rpx;
        border: 1rpx solid #FB8620;
        background-color: transparent;
        padding: 0 8rpx 0 4rpx; 
        box-sizing: border-box;
        align-self: flex-start;
        white-space: nowrap; 

        .spots-label {
				height: 36rpx;
				font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;
				font-weight: normal;
				font-size: 22rpx;
				color: #FB8620;
				text-align: left;
				font-style: normal;
				text-transform: none;
				line-height: 36rpx;
                white-space: nowrap;
			}

			.spots-count {
                width: auto; 
                text-align: right; 
				height: 36rpx;
				font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;
				font-weight: normal;
				font-size: 22rpx;
				color: #FB8620;
				line-height: 36rpx;
				white-space: nowrap;
                font-variant-numeric: tabular-nums;
			}
	}

    .status-tag {
        position: absolute;
        top: 12rpx;
        left: 12rpx;
        width: 90rpx;
        height: 40rpx;
        background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);
        border-radius: 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        color: #23232A;
        font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';
        font-weight: normal;
        font-size: 22rpx;
        text-align: left;
        font-style: normal;
        text-transform: none;

        &.ended {
            background: linear-gradient(90deg, #909399 0%, #C0C4CC 100%);
            color: #FFFFFF;
        }
    }
}
</style>
