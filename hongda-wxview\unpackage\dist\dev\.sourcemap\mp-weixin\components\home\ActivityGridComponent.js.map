{"version": 3, "file": "ActivityGridComponent.js", "sources": ["components/home/<USER>", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvaG9tZS9BY3Rpdml0eUdyaWRDb21wb25lbnQudnVl"], "sourcesContent": ["<template>\n\t<view class=\"activity-section section-wrapper\">\n        <view class=\"section-header\">\n            <view class=\"title-wrapper\">\n                <!-- 【修改】直接动态绑定 style 属性，确保字体在加载后能被正确应用 -->\n                <text class=\"title-main\" :style=\"fontStyle\">{{ titleParts.main }}</text>\n                <text class=\"title-gradient\" :style=\"fontStyle\">{{ titleParts.gradient }}</text>\n            </view>\n\t\t\t<view class=\"section-more\" @click=\"goToEventList\">\n\t\t\t\t<text>更多</text>\n\t\t\t\t<up-icon name=\"arrow-right\" size=\"14\"></up-icon>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view v-if=\"isLoading\" class=\"loading-state\">\n\t\t\t<up-loading-icon mode=\"spinner\" size=\"40\"></up-loading-icon>\n\t\t\t<text class=\"loading-text\">加载中...</text>\n\t\t</view>\n\t\t\n\t\t<view v-else class=\"activity-grid\">\n\t\t\t<view \n\t\t\t\tclass=\"activity-card\" \n\t\t\t\tv-for=\"(item, index) in activityList\" \n\t\t\t\t:key=\"item.id || index\"\n\t\t\t\t@click=\"goToEventDetail(item)\"\n\t\t\t>\n\t\t\t\t<view class=\"image-wrapper\">\n\t\t\t\t\t<image \n\t\t\t\t\t\tclass=\"activity-image\" \n\t\t\t\t\t\t:src=\"item.image\" \n\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t\t:lazy-load=\"true\"\n\t\t\t\t\t></image>\n                    <view class=\"status-tag\" :class=\"item.statusClass\">{{ item.status }}</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"activity-info\">\n\t\t\t\t\t<text class=\"activity-title\">{{ item.title }}</text>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 时间和地点信息行 -->\n\t\t\t\t\t<view class=\"activity-meta-row\">\n                        <view class=\"time-info\">\n                            <image class=\"meta-icon\" :src=\"icons.time\" mode=\"widthFix\" />\n                            <text class=\"time-text\">{{ item.dateTime }}</text>\n                            <text class=\"weekday-text\">{{ item.weekday }}</text>\n                        </view>\n                        <view class=\"location-info\">\n                            <image class=\"meta-icon\" :src=\"icons.location\" mode=\"widthFix\" />\n                            <text class=\"location-text\">{{ item.location }}</text>\n                        </view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n                    <!-- 剩余名额标签 -->\n\t\t\t\t\t<view class=\"remaining-spots-tag\">\n\t\t\t\t\t\t<text class=\"spots-label\">剩余名额：</text>\n\t\t\t\t\t\t<text class=\"spots-count\">{{ item.remainingSpots }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 空状态 -->\n\t\t<view v-if=\"!isLoading && activityList.length === 0\" class=\"empty-state\">\n\t\t\t<up-empty mode=\"data\" text=\"暂无精选活动\" textColor=\"#909399\" iconSize=\"80\"></up-empty>\n\t\t</view>\n\t</view>\n</template>\n\n<script setup>\nimport { ref, onMounted, onUnmounted, computed } from 'vue';\nimport { getHotEventListApi } from '@/api/data/event.js';\nimport { getFullImageUrl } from '@/utils/image.js';\nimport { formatEventStatus, getStatusClass, calculateRemainingSpots } from '@/utils/tools.js';\n\n// --- 【新增】字体加载状态 ---\nconst fontReady = ref(false);\n\n// --- 【新增】动态计算字体样式 ---\nconst fontStyle = computed(() => {\n  if (fontReady.value) {\n    // 字体准备好后，返回包含 font-family 的 style 对象\n    return { fontFamily: \"'YouSheBiaoTiHei', sans-serif\" };\n  }\n  // 否则返回空对象\n  return {};\n});\n\n// --- 【新增】在组件挂载时检查字体状态 ---\nonMounted(() => {\n  const app = getApp();\n  if (app && app.globalData && app.globalData.fontLoaded) {\n    // 如果字体已加载，直接应用样式\n    fontReady.value = true;\n  } else {\n    // 否则，监听全局事件\n    uni.$on('customFontsLoaded', onFontsLoaded);\n  }\n  \n  // 原有的 onMounted 逻辑\n  try {\n    const assets = uni.getStorageSync('staticAssets')\n    icons.time = assets?.detail_icon_time || ''\n    icons.location = assets?.detail_icon_location || ''\n  } catch (e) {}\n  fetchHotEvents();\n});\n\n// --- 【新增】事件回调函数 ---\nconst onFontsLoaded = () => {\n  fontReady.value = true;\n};\n\n// --- 【新增】组件卸载时移除监听 ---\nonUnmounted(() => {\n  uni.$off('customFontsLoaded', onFontsLoaded);\n});\n\n\n// --- 原有代码 (保持不变) ---\nconst activityList = ref([]);\nconst isLoading = ref(false);\n\nconst titleParts = computed(() => {\n  const title = '精选活动';\n  // 【重要】确保标题能被正确分割\n  return { main: title.slice(0, 2), gradient: title.slice(2) };\n});\n\nconst icons = {\n  time: '',\n  location: ''\n};\n\nconst parseSafeDate = (input) => {\n  if (!input) return null;\n  if (input instanceof Date) {\n    return isNaN(input.getTime()) ? null : input;\n  }\n  if (typeof input === 'number') {\n    const d = new Date(input);\n    return isNaN(d.getTime()) ? null : d;\n  }\n  if (typeof input === 'string') {\n    let s = input.trim();\n    if (/^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$/.test(s)) {\n      s = s.replace(' ', 'T');\n    }\n    let d = new Date(s);\n    if (isNaN(d.getTime())) {\n      const m = s.match(/^(\\d{4})-(\\d{1,2})-(\\d{1,2})(?:[ T](\\d{1,2}):(\\d{2})(?::(\\d{2}))?)?/);\n      if (m) {\n        const y = m[1];\n        const mo = m[2];\n        const day = m[3];\n        const rest = m[4] ? ` ${m[4]}:${m[5]}:${m[6] || '00'}` : '';\n        d = new Date(`${y}/${mo}/${day}${rest}`);\n      }\n    }\n    return isNaN(d.getTime()) ? null : d;\n  }\n  return null;\n};\n\nconst formatActivityDate = (dateString) => {\n  if (!dateString) return '';\n  const date = parseSafeDate(dateString);\n  if (!date) return '';\n  const month = date.getMonth() + 1;\n  const day = String(date.getDate()).padStart(2, '0');\n  return `${month}.${day}`;\n};\n\nconst getWeekday = (dateString) => {\n  if (!dateString) return '';\n  const date = parseSafeDate(dateString);\n  if (!date) return '';\n  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];\n  return weekdays[date.getDay()];\n};\n\nconst calculateRemaining = (maxParticipants, registeredCount) => {\n  if (!maxParticipants || maxParticipants <= 0) {\n    return '不限';\n  }\n  const remaining = maxParticipants - (registeredCount || 0);\n  return remaining > 0 ? remaining : 0;\n};\n\nconst formatActivityLocation = (item) => {\n  if (item.city && item.city.trim()) {\n    return item.city.trim();\n  }\n  return '待定';\n};\n\nconst fetchHotEvents = async () => {\n  if (isLoading.value) return;\n  isLoading.value = true;\n  try {\n    const response = await getHotEventListApi(8);\n    const { rows = [] } = response;\n    const registeringRows = rows.filter(item => getStatusClass(item.status) !== 'ended').slice(0, 4);\n    activityList.value = registeringRows.map(item => ({\n      id: item.id,\n      title: item.title,\n      image: getFullImageUrl(item.coverImageUrl),\n      location: formatActivityLocation(item),\n      status: formatEventStatus(item.status),\n      statusClass: getStatusClass(item.status) === 'ended' ? 'status-ended' : 'status-registering',\n      dateTime: formatActivityDate(item.startTime),\n      weekday: getWeekday(item.startTime),\n      remainingSpots: calculateRemaining(item.maxParticipants, item.registeredCount)\n    }));\n  } catch (error) {\n    console.error('获取热门活动失败:', error);\n    activityList.value = [\n      { \n        id: 1, \n        title: '2025 Ozen卖家增长峰会·北京站', \n        status: '报名中', \n        statusClass: 'status-registering', \n        location: '北京', \n        image: 'https://via.placeholder.com/170x100/3c9cff/fff?text=活动1',\n        dateTime: '7.15',\n        weekday: '周二',\n        remainingSpots: '29'\n      },\n    ];\n    uni.showToast({\n      title: '获取活动数据失败，显示示例数据',\n      icon: 'none',\n      duration: 2000\n    });\n  } finally {\n    isLoading.value = false;\n  }\n};\n\nconst goToEventDetail = (activity) => {\n  uni.navigateTo({\n    url: `/pages_sub/pages_event/detail?id=${activity.id}`\n  });\n};\n\nconst goToEventList = () => {\n  uni.switchTab({\n    url: '/pages/event/index',\n    fail: () => {\n      uni.navigateTo({ url: '/pages/event/index' });\n    }\n  });\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.section-wrapper {\n\tmargin: 24rpx;\n\tpadding: 20rpx;\n\tbackground-color: #ffffff;\n\tborder-radius: 16rpx;\n}\n.section-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding-bottom: 20rpx;\n}\n.title-wrapper {\n    display: flex;\n    align-items: center;\n    /* 【修改】移除这里的 font-family，让 JS 动态 style 来控制 */\n}\n.title-main {\n    /* “精选”的样式 */\n    font-size: 40rpx;\n    font-weight: 400; /* 优设标题黑通常只有一个字重 */\n    color: #023F98;\n}\n.title-gradient {\n    /* “活动”的样式 */\n    font-size: 40rpx;\n    font-weight: 400;\n    /* 应用您指定的精确渐变色 */\n    background-image: linear-gradient(91.61deg, #FFAD22 0%, #FFBB87 100%);\n    /* 关键：实现文字渐变效果 */\n    -webkit-background-clip: text;\n    background-clip: text;\n    color: transparent;\n}\n.section-more {\n\tdisplay: flex;\n\talign-items: center;\n\tfont-size: 28rpx;\n\tcolor: #909399;\n\tcursor: pointer;\n\ttransition: color 0.2s ease;\n\t\n\t&:active {\n\t\tcolor: #f56c6c;\n\t}\n}\n\n// 加载状态样式\n.loading-state {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 60rpx 0;\n\t\n\t.loading-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #909399;\n\t\tmargin-top: 20rpx;\n\t}\n}\n\n// 空状态样式\n.empty-state {\n\tpadding: 60rpx 0;\n\ttext-align: center;\n}\n\n.activity-grid {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tjustify-content: space-between;\n}\n.activity-card {\n\twidth: calc(50% - 10rpx);\n\tbackground-color: #ffffff;\n\tborder-radius: 16rpx;\n\tmargin-bottom: 20rpx;\n\toverflow: hidden;\n\tbox-shadow: none;\n\ttransition: transform 0.2s ease, box-shadow 0.2s ease;\n\n\t// 点击效果\n\t&:active {\n\t\ttransform: scale(0.98);\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.image-wrapper {\n\t\tposition: relative;\n\t\twidth: 336rpx;\n\t\theight: 192rpx; \n\t\tmargin: 0 auto; \n\t\tborder-radius: 16rpx; \n\t\toverflow: hidden;\n\t}\n\n\t.activity-image {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tborder-radius: 16rpx;\n\t}\n\n\t.activity-info {\n\t\tpadding: 16rpx;\n\t}\n\n\t.activity-title {\n\t\twidth: 100%;\n\t\theight: 80rpx;\n\t\tfont-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;\n\t\tfont-weight: normal;\n\t\tfont-size: 28rpx;\n\t\tcolor: #23232A;\n\t\ttext-align: justify;\n\t\tfont-style: normal;\n\t\ttext-transform: none;\n\t\tline-height: 40rpx; \n\t\tmargin-bottom: 12rpx;\n\t\tword-break: break-word;\n\t\tdisplay: -webkit-box;\n\t\t-webkit-box-orient: vertical;\n\t\tline-clamp: 2; \n\t\t-webkit-line-clamp: 2;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t}\n\n    // 时间和地点信息行\n    .activity-meta-row {\n        display: flex;\n        align-items: center;\n        justify-content: flex-start;\n        gap: 46rpx; \n        margin-bottom: 12rpx;\n\t\t\n        .time-info {\n            display: flex;\n            align-items: center;\n            flex: none;\n\t\t\t\n\t\t\t.meta-icon {\n\t\t\t\twidth: 24rpx;\n\t\t\t\theight: 24rpx;\n\t\t\t}\n\t\t\t\n            .time-text {\n\t\t\t\twidth: 66rpx;\n\t\t\t\theight: 32rpx;\n\t\t\t\tfont-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;\n\t\t\t\tfont-weight: normal;\n\t\t\t\tfont-size: 22rpx;\n\t\t\t\tcolor: #9B9A9A;\n\t\t\t\ttext-align: left;\n\t\t\t\tfont-style: normal;\n\t\t\t\ttext-transform: none;\n\t\t\t\tline-height: 32rpx;\n                margin-left: 6rpx;\n                margin-right: 6rpx; \n\t\t\t}\n\t\t\t\n\t\t\t.weekday-text {\n\t\t\t\tfont-size: 22rpx;\n\t\t\t\tcolor: #9B9A9A;\n\t\t\t\tline-height: 32rpx;\n\t\t\t}\n\t\t}\n\t\t\n        .location-info {\n            display: flex;\n            align-items: center;\n            flex: none; \n\t\t\t\n\t\t\t.meta-icon {\n\t\t\t\twidth: 24rpx;\n\t\t\t\theight: 24rpx;\n\t\t\t}\n\t\t\t\n\t\t\t.location-text {\n\t\t\t\twidth: 66rpx;\n\t\t\t\theight: 32rpx;\n\t\t\t\tfont-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;\n\t\t\t\tfont-weight: normal;\n\t\t\t\tfont-size: 22rpx;\n\t\t\t\tcolor: #9B9A9A;\n\t\t\t\ttext-align: left;\n\t\t\t\tfont-style: normal;\n\t\t\t\ttext-transform: none;\n\t\t\t\tline-height: 32rpx;\n\t\t\t\tmargin-left: 8rpx;\n\t\t\t}\n\t\t}\n\t}\n\n    // 剩余名额\n\t.remaining-spots-tag {\n        display: inline-flex;\n        align-items: center;\n        justify-content: flex-start;\n        height: 40rpx;\n        border-radius: 4rpx;\n        border: 1rpx solid #FB8620;\n        background-color: transparent;\n        padding: 0 8rpx 0 4rpx; \n        box-sizing: border-box;\n        align-self: flex-start;\n        white-space: nowrap; \n\n        .spots-label {\n\t\t\t\theight: 36rpx;\n\t\t\t\tfont-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;\n\t\t\t\tfont-weight: normal;\n\t\t\t\tfont-size: 22rpx;\n\t\t\t\tcolor: #FB8620;\n\t\t\t\ttext-align: left;\n\t\t\t\tfont-style: normal;\n\t\t\t\ttext-transform: none;\n\t\t\t\tline-height: 36rpx;\n                white-space: nowrap;\n\t\t\t}\n\n\t\t\t.spots-count {\n                width: auto; \n                text-align: right; \n\t\t\t\theight: 36rpx;\n\t\t\t\tfont-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;\n\t\t\t\tfont-weight: normal;\n\t\t\t\tfont-size: 22rpx;\n\t\t\t\tcolor: #FB8620;\n\t\t\t\tline-height: 36rpx;\n\t\t\t\twhite-space: nowrap;\n                font-variant-numeric: tabular-nums;\n\t\t\t}\n\t}\n\n    .status-tag {\n        position: absolute;\n        top: 12rpx;\n        left: 12rpx;\n        width: 90rpx;\n        height: 40rpx;\n        background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);\n        border-radius: 20rpx;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        box-sizing: border-box;\n        color: #23232A;\n        font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\n        font-weight: normal;\n        font-size: 22rpx;\n        text-align: left;\n        font-style: normal;\n        text-transform: none;\n\n        &.ended {\n            background: linear-gradient(90deg, #909399 0%, #C0C4CC 100%);\n            color: #FFFFFF;\n        }\n    }\n}\n</style>\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/home/<USER>'\nwx.createComponent(Component)"], "names": ["ref", "computed", "onMounted", "uni", "onUnmounted", "getHotEventListApi", "getStatusClass", "getFullImageUrl", "formatEventStatus"], "mappings": ";;;;;;;;;;;;;;;;;;;;AA0EA,UAAM,YAAYA,cAAAA,IAAI,KAAK;AAG3B,UAAM,YAAYC,cAAQ,SAAC,MAAM;AAC/B,UAAI,UAAU,OAAO;AAEnB,eAAO,EAAE,YAAY;MACtB;AAED,aAAO;IACT,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AACd,YAAM,MAAM;AACZ,UAAI,OAAO,IAAI,cAAc,IAAI,WAAW,YAAY;AAEtD,kBAAU,QAAQ;AAAA,MACtB,OAAS;AAELC,sBAAAA,MAAI,IAAI,qBAAqB,aAAa;AAAA,MAC3C;AAGD,UAAI;AACF,cAAM,SAASA,cAAAA,MAAI,eAAe,cAAc;AAChD,cAAM,QAAO,iCAAQ,qBAAoB;AACzC,cAAM,YAAW,iCAAQ,yBAAwB;AAAA,MACrD,SAAW,GAAG;AAAA,MAAE;AACd;IACF,CAAC;AAGD,UAAM,gBAAgB,MAAM;AAC1B,gBAAU,QAAQ;AAAA,IACpB;AAGAC,kBAAAA,YAAY,MAAM;AAChBD,oBAAAA,MAAI,KAAK,qBAAqB,aAAa;AAAA,IAC7C,CAAC;AAID,UAAM,eAAeH,cAAAA,IAAI,CAAA,CAAE;AAC3B,UAAM,YAAYA,cAAAA,IAAI,KAAK;AAE3B,UAAM,aAAaC,cAAQ,SAAC,MAAM;AAChC,YAAM,QAAQ;AAEd,aAAO,EAAE,MAAM,MAAM,MAAM,GAAG,CAAC,GAAG,UAAU,MAAM,MAAM,CAAC,EAAC;AAAA,IAC5D,CAAC;AAED,UAAM,QAAQ;AAAA,MACZ,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAEA,UAAM,gBAAgB,CAAC,UAAU;AAC/B,UAAI,CAAC;AAAO,eAAO;AACnB,UAAI,iBAAiB,MAAM;AACzB,eAAO,MAAM,MAAM,QAAS,CAAA,IAAI,OAAO;AAAA,MACxC;AACD,UAAI,OAAO,UAAU,UAAU;AAC7B,cAAM,IAAI,IAAI,KAAK,KAAK;AACxB,eAAO,MAAM,EAAE,QAAS,CAAA,IAAI,OAAO;AAAA,MACpC;AACD,UAAI,OAAO,UAAU,UAAU;AAC7B,YAAI,IAAI,MAAM;AACd,YAAI,wCAAwC,KAAK,CAAC,GAAG;AACnD,cAAI,EAAE,QAAQ,KAAK,GAAG;AAAA,QACvB;AACD,YAAI,IAAI,IAAI,KAAK,CAAC;AAClB,YAAI,MAAM,EAAE,QAAO,CAAE,GAAG;AACtB,gBAAM,IAAI,EAAE,MAAM,qEAAqE;AACvF,cAAI,GAAG;AACL,kBAAM,IAAI,EAAE,CAAC;AACb,kBAAM,KAAK,EAAE,CAAC;AACd,kBAAM,MAAM,EAAE,CAAC;AACf,kBAAM,OAAO,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,IAAI,KAAK;AACzD,gBAAI,oBAAI,KAAK,GAAG,CAAC,IAAI,EAAE,IAAI,GAAG,GAAG,IAAI,EAAE;AAAA,UACxC;AAAA,QACF;AACD,eAAO,MAAM,EAAE,QAAS,CAAA,IAAI,OAAO;AAAA,MACpC;AACD,aAAO;AAAA,IACT;AAEA,UAAM,qBAAqB,CAAC,eAAe;AACzC,UAAI,CAAC;AAAY,eAAO;AACxB,YAAM,OAAO,cAAc,UAAU;AACrC,UAAI,CAAC;AAAM,eAAO;AAClB,YAAM,QAAQ,KAAK,SAAQ,IAAK;AAChC,YAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,aAAO,GAAG,KAAK,IAAI,GAAG;AAAA,IACxB;AAEA,UAAM,aAAa,CAAC,eAAe;AACjC,UAAI,CAAC;AAAY,eAAO;AACxB,YAAM,OAAO,cAAc,UAAU;AACrC,UAAI,CAAC;AAAM,eAAO;AAClB,YAAM,WAAW,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAC1D,aAAO,SAAS,KAAK,OAAM,CAAE;AAAA,IAC/B;AAEA,UAAM,qBAAqB,CAAC,iBAAiB,oBAAoB;AAC/D,UAAI,CAAC,mBAAmB,mBAAmB,GAAG;AAC5C,eAAO;AAAA,MACR;AACD,YAAM,YAAY,mBAAmB,mBAAmB;AACxD,aAAO,YAAY,IAAI,YAAY;AAAA,IACrC;AAEA,UAAM,yBAAyB,CAAC,SAAS;AACvC,UAAI,KAAK,QAAQ,KAAK,KAAK,KAAI,GAAI;AACjC,eAAO,KAAK,KAAK;MAClB;AACD,aAAO;AAAA,IACT;AAEA,UAAM,iBAAiB,YAAY;AACjC,UAAI,UAAU;AAAO;AACrB,gBAAU,QAAQ;AAClB,UAAI;AACF,cAAM,WAAW,MAAMI,kCAAmB,CAAC;AAC3C,cAAM,EAAE,OAAO,GAAI,IAAG;AACtB,cAAM,kBAAkB,KAAK,OAAO,UAAQC,YAAAA,eAAe,KAAK,MAAM,MAAM,OAAO,EAAE,MAAM,GAAG,CAAC;AAC/F,qBAAa,QAAQ,gBAAgB,IAAI,WAAS;AAAA,UAChD,IAAI,KAAK;AAAA,UACT,OAAO,KAAK;AAAA,UACZ,OAAOC,YAAAA,gBAAgB,KAAK,aAAa;AAAA,UACzC,UAAU,uBAAuB,IAAI;AAAA,UACrC,QAAQC,YAAAA,kBAAkB,KAAK,MAAM;AAAA,UACrC,aAAaF,YAAc,eAAC,KAAK,MAAM,MAAM,UAAU,iBAAiB;AAAA,UACxE,UAAU,mBAAmB,KAAK,SAAS;AAAA,UAC3C,SAAS,WAAW,KAAK,SAAS;AAAA,UAClC,gBAAgB,mBAAmB,KAAK,iBAAiB,KAAK,eAAe;AAAA,QAC9E,EAAC;AAAA,MACH,SAAQ,OAAO;AACdH,sBAAA,MAAA,MAAA,SAAA,oDAAc,aAAa,KAAK;AAChC,qBAAa,QAAQ;AAAA,UACnB;AAAA,YACE,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,aAAa;AAAA,YACb,UAAU;AAAA,YACV,OAAO;AAAA,YACP,UAAU;AAAA,YACV,SAAS;AAAA,YACT,gBAAgB;AAAA,UACjB;AAAA,QACP;AACIA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAAA,MACL,UAAY;AACR,kBAAU,QAAQ;AAAA,MACnB;AAAA,IACH;AAEA,UAAM,kBAAkB,CAAC,aAAa;AACpCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,oCAAoC,SAAS,EAAE;AAAA,MACxD,CAAG;AAAA,IACH;AAEA,UAAM,gBAAgB,MAAM;AAC1BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,QACL,MAAM,MAAM;AACVA,wBAAAA,MAAI,WAAW,EAAE,KAAK,qBAAsB,CAAA;AAAA,QAC7C;AAAA,MACL,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzPA,GAAG,gBAAgB,SAAS;"}