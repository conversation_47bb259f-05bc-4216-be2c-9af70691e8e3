<template>
  <view v-if="visible" class="popup-overlay" @touchmove.prevent>
    <view class="popup-content">
      <image
          :src="adData.imageUrl"
          class="popup-image"
          mode="aspectFill"
          @click="handleAdClick"
      />
      <view class="close-button" @click="closePopup">
        <uni-icons type="closeempty" size="20" color="#fff"></uni-icons>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
// 导入我们统一的导航工具函数
import { navigateTo } from '@/utils/navigation.js';

// 定义 props，用于从父组件接收广告数据和显示状态
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  adData: {
    type: Object,
    default: () => ({})
  }
});

// 定义 emit 事件，用于通知父组件关闭弹窗
const emit = defineEmits(['close']);

const visible = ref(props.show);

// 监听父组件传入的 show 属性变化，同步控制弹窗的显示/隐藏
watch(() => props.show, (newVal) => {
  console.log('弹窗显示状态变化:', newVal);
  console.log('广告数据:', props.adData);
  visible.value = newVal;
}, { immediate: true });

// 计算属性确保数据可用
const adData = computed(() => {
  return props.adData || {};
});

// 处理广告图片的点击事件
const handleAdClick = () => {
  console.log('广告被点击:', props.adData);
  // 直接调用 navigateTo 函数，它能智能处理 linkType 和 linkTarget
  if (props.adData && typeof navigateTo === 'function') {
    navigateTo(props.adData);
  }
  // 点击后也关闭弹窗
  closePopup();
};

// 关闭弹窗的函数
const closePopup = () => {
  console.log('关闭弹窗');
  visible.value = false;
  emit('close'); // 通知父组件更新状态
};
</script>

<style lang="scss" scoped>
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.popup-content {
  position: relative;
  /* 按照设计稿的尺寸设置 */
  width: 506rpx;
  height: 900rpx;
  border-radius: 0rpx;
  overflow: visible; /* 改为 visible，让关闭按钮能显示在外面 */
  background-color: #fff;
}

.popup-image {
  width: 100%;
  height: 100%;
  border-radius: 0rpx;
  display: block;
}

.close-button {
  position: absolute;
  /* 将关闭按钮放在图片正下方 */
  bottom: -100rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  border: 2rpx solid #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.3);
}

.close-button:active {
  opacity: 0.7;
  transform: scale(0.95);
}
</style>