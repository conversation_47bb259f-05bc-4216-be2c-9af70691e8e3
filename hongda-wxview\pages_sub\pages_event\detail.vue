<template>
  <view class="event-detail-page">
    <!-- Top Navigation Bar -->
    <up-navbar
      title="活动详情"
      :fixed="true"
      :safeAreaInsetTop="true"
      bgColor="#ffffff"
      leftIcon="arrow-left"
      leftIconColor="#333333"
      titleStyle="color: #333333; font-weight: bold;"
      @leftClick="handleNavBack"
    >
    </up-navbar>

    <!-- 第一层防护：全局加载状态 -->
    <view class="loading-container" v-if="isLoading">
      <up-loading-page loadingText="正在加载活动详情..." loadingMode="spinner"></up-loading-page>
    </view>

    <!-- 第二层防护：页面主要内容，只有在数据完全准备好时才渲染 -->
    <scroll-view scroll-y class="scroll-content" :style="{ paddingTop: totalTopPadding }" v-if="!isLoading && eventDetail">
      <!-- Top Event Image -->
      <view class="top-image-container">
        <up-image :src="getFullImageUrl(eventDetail.coverImageUrl)" mode="aspectFill" height="420rpx" width="100%"></up-image>
      </view>

      <!-- Middle Information Card -->
      <EventInfoCard :eventDetail="eventDetail" />

      <!-- 活动详情富文本区域 -->
      <EventDetailContent :eventDetail="eventDetail" />

      <!-- 底部留白，为固定操作栏腾出空间 -->
      <view class="bottom-spacer"></view>
    </scroll-view>

    <!-- 底部操作栏：只有在数据加载完成且存在时才显示 -->
    <EventActionBar
      v-if="!isLoading && eventDetail"
      :eventDetail="eventDetail"
      :isLoading="isLoading"
      :registrationStatus="registrationStatus"
      :isButtonDisabled="isButtonDisabled"
      :buttonText="buttonText"
      @share="handleShare"
      @register="handleRegistration"
    />
  </view>
</template>

<script setup>
import {computed, onMounted, ref, watch} from 'vue';
import {onLoad, onShareAppMessage, onShow, onUnload} from '@dcloudio/uni-app';
import {getEventDetailApi} from '@/api/data/event.js';
import {checkRegistrationStatusApi} from '@/pages_sub/pages_event/api/data/registration.js';
import {getFullImageUrl} from '@/utils/image.js';
import EventInfoCard from '@event/EventInfoCard.vue'
import EventDetailContent from '@event/EventDetailContent.vue'
import EventActionBar from '@event/EventActionBar.vue'

const eventId = ref(null);
const eventDetail = ref(null);
//报名状态从 eventDetail 中完全分离出来
//registrationStatus可选值：'loading', 'unregistered', 'registered', 'not_logged_in', 'error'
const registrationStatus = ref('loading');
const isLoading = ref(true);
const loginCheckTrigger = ref(0); // 新增：强制触发登录状态重新计算的标记

//动态获取状态栏高度
const statusBarHeight = ref(0);
const navBarHeight = ref(44); 

// 计算总的顶部padding
const totalTopPadding = computed(() => {
  return `${statusBarHeight.value + navBarHeight.value}px`;
});

onMounted(() => {
  // 获取系统信息
  const systemInfo = uni.getSystemInfoSync();
  statusBarHeight.value = systemInfo.statusBarHeight || 0;
});

// 从本地存储获取用户token
const getUserToken = () => {
  try {
    return uni.getStorageSync('token') || null;
  } catch (e) {
    return null;
  }
};

// 检查用户是否已登录
const isLoggedIn = computed(() => {
  // 通过 loginCheckTrigger 强制重新计算
  loginCheckTrigger.value; // eslint-disable-line no-unused-expressions
  return !!getUserToken();
});

/**
 * 计算活动的报名状态，与后端RegistrationStatus.calculateStatus逻辑保持一致
 * 0: 即将开始 (当前时间 < 报名开始时间)
 * 1: 报名中 (报名开始时间 <= 当前时间 <= 报名结束时间)  
 * 2: 报名截止 (当前时间 > 报名结束时间)
 */
const eventRegistrationStatus = computed(() => {
  const registrationStartTime = eventDetail.value?.registrationStartTime
  const registrationEndTime = eventDetail.value?.registrationEndTime
  
  try {
    const now = new Date()
    
    // 未开始：报名开始时间不为空 && 当前时间 < 报名开始时间
    if (registrationStartTime && now < new Date(registrationStartTime)) {
      return 0 // 即将开始
    }
    // 报名中：(报名开始时间为空 || 当前时间 >= 报名开始时间) && (报名结束时间为空 || 当前时间 <= 报名结束时间)
    else if ((!registrationStartTime || now >= new Date(registrationStartTime)) && 
             (!registrationEndTime || now <= new Date(registrationEndTime))) {
      return 1 // 报名中
    }
    // 已结束：报名结束时间不为空 && 当前时间 > 报名结束时间
    else if (registrationEndTime && now > new Date(registrationEndTime)) {
      return 2 // 报名截止
    }
    
    // 默认返回报名中（如果时间配置不明确）
    return 1
  } catch (error) {
    console.warn('报名状态计算失败:', error)
    return 1 // 默认显示报名中
  }
})

// 🔄 状态分离重构：基于独立的 registrationStatus 和活动状态计算按钮文字
const buttonText = computed(() => {
  if (!eventDetail.value) return '加载中...';
  
  // 根据独立的 registrationStatus 状态决定按钮文字
  switch (registrationStatus.value) {
    case 'loading':
      return '检查状态中...';
    case 'registered':
      return '已报名';
    case 'not_logged_in':
      // 未登录时根据活动状态显示不同文字
      switch (eventRegistrationStatus.value) {
        case 0: return '即将开始';
        case 1: return '立即报名';
        case 2: return '报名截止';
        default: return '立即报名';
      }
    case 'unregistered':
      // 已登录但未报名时根据活动状态显示不同文字
      switch (eventRegistrationStatus.value) {
        case 0: return '即将开始';
        case 1: return '立即报名';
        case 2: return '报名截止';
        default: return '立即报名';
      }
    case 'error':
      // 出错时根据活动状态显示不同文字
      switch (eventRegistrationStatus.value) {
        case 0: return '即将开始';
        case 1: return '立即报名';
        case 2: return '报名截止';
        default: return '立即报名';
      }
    default:
      return '立即报名';
  }
});

// 状态分离重构：基于独立状态计算按钮是否禁用
const isButtonDisabled = computed(() => {
  if (!eventDetail.value) return true;
  
  // 如果状态还在加载中，禁用按钮
  if (registrationStatus.value === 'loading') return true;
  
  // 如果用户已报名，禁用按钮
  if (registrationStatus.value === 'registered') return true;
  
  // 根据活动的报名状态判断是否禁用
  // 即将开始(0)和报名截止(2)时禁用，报名中(1)时启用
  return eventRegistrationStatus.value === 0 || eventRegistrationStatus.value === 2;
});

// 子组件内已处理：时间、剩余名额和富文本

// 智能导航回退处理函数
const handleNavBack = () => {
  console.log('=== 开始智能导航回退处理 ===');
  
  // 第一步：尝试正常回退
  uni.navigateBack({
    success: () => {
      console.log('正常回退成功');
    },
    fail: (err) => {
      console.warn('正常回退失败:', err);
      
      // 第二步：尝试跳转到活动列表页面
      uni.navigateTo({
        url: '/pages/event/index',
        success: () => {
          console.log('跳转到活动列表页面成功');
        },
        fail: (err2) => {
          console.warn('跳转到活动列表页面失败:', err2);
          
          // 第三步：最后的兜底方案，跳转到首页
          uni.switchTab({
            url: '/pages/index/index',
            success: () => {
              console.log('跳转到首页成功');
            },
            fail: (err3) => {
              console.error('所有导航方案都失败了:', err3);
              uni.showToast({
                title: '导航失败，请重新打开小程序',
                icon: 'none'
              });
            }
          });
        }
      });
    }
  });
};

// 🔄 状态分离重构：新的独立报名状态检查函数
const fetchRegistrationStatus = async () => {
  console.log('=== 开始独立的报名状态检查 ===');
  
  try {
    // 1. 检查登录状态
    const token = getUserToken();
    if (!token) {
      console.log('用户未登录，设置状态为 not_logged_in');
      registrationStatus.value = 'not_logged_in';
      return;
    }

    // 2. 检查必要的前置条件
    if (!eventId.value) {
      console.warn('缺少 eventId，无法检查报名状态');
      registrationStatus.value = 'error';
      return;
    }

    console.log('前置条件满足，开始调用 API 检查报名状态...');
    
    // 3. 先检查本地存储的报名状态（快速反馈）
    try {
      const localRegistrationStatus = uni.getStorageSync('registrationStatus') || {};
      const localStatus = localRegistrationStatus[eventId.value];
      if (localStatus && localStatus.isRegistered) {
        console.log('从本地存储发现已报名状态，立即更新 UI');
        registrationStatus.value = 'registered';
      }
    } catch (error) {
      console.warn('读取本地报名状态失败:', error);
    }

    // 4. 调用 API 获取最新状态
    const statusRes = await checkRegistrationStatusApi(eventId.value);
    
    if (statusRes.code === 200) {
      // 修复：根据后端 AjaxResult 的实际结构访问数据
      // AjaxResult 返回的数据直接在根级别：{code: 200, msg: "操作成功", isRegistered: true/false}
      const isRegistered = statusRes.isRegistered || false;
      console.log('获取报名状态成功:', isRegistered);
      
      const newStatus = isRegistered ? 'registered' : 'unregistered';
      
      // 5. 更新状态（只更新 registrationStatus，不涉及 eventDetail）
      if (registrationStatus.value !== newStatus) {
        console.log('报名状态发生变化:', registrationStatus.value, '->', newStatus);
        registrationStatus.value = newStatus;
        
        // 显示状态更新提示
        if (newStatus === 'registered' && registrationStatus.value !== 'registered') {
          uni.$u.toast('报名状态已更新');
        }
      } else {
        console.log('报名状态无变化，保持现状');
      }
      
    } else {
      console.warn('检查报名状态 API 返回错误:', statusRes);
      registrationStatus.value = 'error';
    }

  } catch (error) {
    console.error('报名状态检查失败:', error);
    registrationStatus.value = 'error';
  }
  
  console.log('=== 报名状态检查完成，最终状态:', registrationStatus.value, ' ===');
};

// 页面加载
onLoad(async (options) => {
  eventId.value = options.id;
  if (!eventId.value) {
    uni.$u.toast('活动不存在');
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
    return;
  }

  // 【数据实时更新方案】监听全局数据变化事件
  uni.$on('dataChanged', async () => {
    console.log('📩 收到数据变化事件，重新获取活动详情...');
    
    if (eventId.value) {
      try {
        console.log('开始重新获取活动详情数据...');
        
        // 重新获取活动详情（包含最新的报名人数）
        const detailRes = await getEventDetailApi(eventId.value);
        
        if (detailRes.code === 200) {
          // 更新活动详情数据
          const rawData = detailRes.data;
          rawData.details = rawData.details || '';
          rawData.summary = rawData.summary || '';
          rawData.title = rawData.title || '';
          rawData.location = rawData.location || '';
          rawData.coverImageUrl = rawData.coverImageUrl || '';
          
          eventDetail.value = rawData;
          console.log('活动详情数据已更新，最新报名人数:', rawData.registeredCount);
          
          // 同时刷新报名状态
          await fetchRegistrationStatus();
          
          uni.showToast({
            title: '数据已更新',
            icon: 'success',
            duration: 1500
          });
        }
      } catch (error) {
        console.error('重新获取活动详情失败:', error);
      }
    } else {
      console.warn('事件监听：缺少 eventId，跳过数据刷新');
    }
  });

  try {
    console.log('开始加载活动详情，eventId:', eventId.value);
    
    // 状态分离重构：第一步 - 严格串行加载活动详情
    const detailRes = await getEventDetailApi(eventId.value);
    
    if (detailRes.code === 200) {
      console.log('活动详情API调用成功，开始处理数据...');
      
      // --- 数据清洗和安全处理 ---
      const rawData = detailRes.data;
      
      // 清洗 details 字段：确保它是一个安全的字符串
      if (!rawData.details || typeof rawData.details !== 'string') {
        rawData.details = '';
      } else {
        rawData.details = rawData.details.trim();
      }
      
      // 清洗 summary 字段
      if (!rawData.summary || typeof rawData.summary !== 'string') {
        rawData.summary = '';
      } else {
        rawData.summary = rawData.summary.trim();
      }
      
      // 确保其他关键字段的安全性
      rawData.title = rawData.title || '';
      rawData.location = rawData.location || '';
      rawData.coverImageUrl = rawData.coverImageUrl || '';
      
      // 关键：确保eventDetail完全赋值后才继续
      eventDetail.value = rawData;
      console.log('活动详情数据已完全加载并赋值:', {
        id: eventDetail.value.id,
        title: eventDetail.value.title,
        status: eventDetail.value.status
      });
      
    } else {
      throw new Error(detailRes.msg || '获取活动详情失败');
    }

    // 状态分离重构：第二步 - 串行调用独立的报名状态检查
    console.log('活动详情加载完成，现在开始检查报名状态...');
    await fetchRegistrationStatus();
    console.log('报名状态检查完成');

  } catch (error) {
    console.error('获取活动详情失败', error);
    uni.$u.toast(error.message || '加载失败，请稍后重试');
    // 即使活动详情加载失败，也要设置报名状态为错误
    registrationStatus.value = 'error';
  } finally {
    // 确保无论成功还是失败，都要停止loading状态
    console.log('页面加载流程完成，停止loading状态');
    isLoading.value = false;
  }
});

// 状态分离重构：简化 onShow 逻辑
onShow(async () => {
  console.log('=== 活动详情页面 onShow 触发 ===');
  
  // 使用调试工具检查登录状态
  // 开发期调试：已移除 loginDebugUtils
  
  // 强制触发 isLoggedIn 计算属性重新计算
  loginCheckTrigger.value++;
  
  const currentToken = getUserToken();
  console.log('当前登录状态:', !!currentToken);
  console.log('当前token:', currentToken);
  console.log('当前活动ID:', eventId.value);
  console.log('活动详情是否已加载:', !!eventDetail.value);
  console.log('页面是否还在加载中:', isLoading.value);
  console.log('当前报名状态:', registrationStatus.value);
  
  // 状态分离重构：检查是否有状态刷新标记（来自报名页面的返回）
  try {
    const refreshFlag = uni.getStorageSync('needRefreshRegistrationStatus');
    if (refreshFlag && refreshFlag.eventId === eventId.value) {
      console.log('发现状态刷新标记，立即刷新报名状态:', refreshFlag);
      // 清除标记，避免重复刷新
      uni.removeStorageSync('needRefreshRegistrationStatus');
      // 强制刷新状态
      if (eventId.value) {
        await fetchRegistrationStatus();
        return; // 直接返回，避免重复检查
      }
    }
  } catch (e) {
    console.warn('检查状态刷新标记失败:', e);
  }

  // 🔄 状态分离重构：只要有 eventId 就可以检查报名状态，不再依赖 eventDetail 加载状态
  if (!isLoading.value && eventId.value) {
    console.log('条件满足，开始重新检查报名状态...');
    await fetchRegistrationStatus();
  } else {
    console.log('跳过报名状态检查，原因:', {
      isLoading: isLoading.value,
      hasEventId: !!eventId.value
    });
    
    // 如果页面还在加载中，等待加载完成后再检查
    if (isLoading.value && eventId.value) {
      console.log('页面正在加载中，等待加载完成后再检查报名状态...');
      const unwatch = watch(isLoading, async (newVal) => {
        if (!newVal) {
          console.log('页面加载完成，现在开始检查报名状态...');
          await fetchRegistrationStatus();
          unwatch();
        }
      });
    }
  }
});

// 🗑️ 移除旧的 checkRegistrationStatusSafely 函数，被 fetchRegistrationStatus 替代

// 页面卸载时移除事件监听
onUnload(() => {
  uni.$off('dataChanged');
});

// 分享功能
onShareAppMessage(() => {
  return {
    title: eventDetail.value?.title || '精彩活动推荐',
    path: `/pages/event/detail?id=${eventId.value}`,
    imageUrl: getFullImageUrl(eventDetail.value?.coverImageUrl)
  };
});

// 状态分离重构：更新报名处理逻辑
const handleRegistration = () => {
  console.log('=== 点击报名按钮 ===');
  console.log('按钮当前显示文字:', buttonText.value);
  console.log('按钮是否禁用:', isButtonDisabled.value);
  console.log('当前报名状态:', registrationStatus.value);
  
  // 使用调试工具检查当前状态
  // 开发期调试：已移除 loginDebugUtils
  
  console.log('当前登录状态:', isLoggedIn.value);
  console.log('当前token:', getUserToken());
  console.log('活动详情:', {
    id: eventDetail.value?.id,
    title: eventDetail.value?.title,
    status: eventDetail.value?.status
  });
  
  // 强制触发登录状态重新检查
  loginCheckTrigger.value++;
  
  // 检查登录状态
  if (!isLoggedIn.value || registrationStatus.value === 'not_logged_in') {
    console.log('用户未登录，跳转到登录页');
    // 开发期调试：已移除 loginDebugUtils.logNavigation
    uni.navigateTo({ url: '/pages_sub/pages_other/login' });
    return;
  }

  console.log('用户已登录');
  
  if (registrationStatus.value === 'registered') {
    // 已报名，只显示提示信息，不跳转
    console.log('用户已报名此活动');
    uni.$u.toast('您已报名此活动');
  } else if (registrationStatus.value === 'unregistered' || registrationStatus.value === 'error') {
    // 检查活动状态，决定是否允许报名
    const currentEventStatus = eventRegistrationStatus.value;
    
    if (currentEventStatus === 0) {
      // 即将开始
      console.log('活动尚未开始');
      uni.$u.toast('活动尚未开始');
    } else if (currentEventStatus === 2) {
      // 报名截止
      console.log('活动报名已截止');
      uni.$u.toast('活动报名已截止');
    } else if (currentEventStatus === 1) {
      // 报名中，可以跳转到报名页面
      console.log('用户未报名，跳转到报名页面');
      console.log('跳转参数:', {
        id: eventId.value,
        title: eventDetail.value?.title
      });
      // 开发期调试：已移除 loginDebugUtils.logNavigation
      uni.navigateTo({
        url: `/pages_sub/pages_event/registration?id=${eventId.value}&title=${encodeURIComponent(eventDetail.value?.title || '')}`
      });
    } else {
      // 其他状态
      console.log('活动状态异常');
      uni.$u.toast('活动状态异常，请稍后再试');
    }
  } else {
    // 状态还在加载中
    console.log('报名状态还在加载中，请稍后再试');
    uni.$u.toast('正在检查报名状态，请稍后再试');
  }
};

// 分享按钮点击
const handleShare = () => {
  // 小程序分享功能
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 0,
    href: `/pages/event/detail?id=${eventId.value}`,
    title: eventDetail.value?.title || '精彩活动推荐',
    summary: eventDetail.value?.summary || '',
    imageUrl: getFullImageUrl(eventDetail.value?.coverImageUrl),
    success: function (res) {
      uni.$u.toast('分享成功');
    },
    fail: function (err) {
      console.error('分享失败:', err);
    }
  });
};
</script>

<style lang="scss" scoped>
.event-detail-page {
  background: #FFFFFF;
  width: 750rpx;
  height: 1624rpx;
  border-radius: 0rpx 0rpx 0rpx 0rpx;
}

.scroll-content {
  flex: 1;
  padding-bottom: 120rpx;
  box-sizing: border-box;
}

.top-image-container {
  width: 100%;
  height: 420rpx; /* 顶部大图高度 */
}

.info-card {
  background: #FFFFFF;
  margin: 0 30rpx; 
  margin-top: -60rpx; 
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(2,63,152,0.1);
  padding: 30rpx; 
  position: relative; 
  z-index: 1; 
}

.info-icon {
  width: 32rpx;
  height: 32rpx;
}

.share-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx; /* 控制图标和文字的间距 */
}

.share-text {
  width: 56rpx;
  height: 44rpx;
  font-family: "Alibaba PuHuiTi 3.0", "Alibaba PuHuiTi 30", sans-serif;
  font-weight: normal;
  font-size: 28rpx;
  color: #023F98; /* 更新为蓝色 */
  line-height: 44rpx;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.share-button-content {
  display: flex;
  flex-direction: row; 
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.card-header {
  display: flex;
  align-items: flex-start; /* 关键：改为顶部对齐 */
  margin-bottom: 20rpx;
}

.status-tag-detail {
   width: 90rpx;
   height: 40rpx;
   display: flex;
   justify-content: center;
   align-items: center;
   position: relative;
   overflow: hidden;
   font-size: 22rpx;
   border-radius: 12rpx;
   margin-right: 16rpx;
   margin-top: 15rpx;
   flex-shrink: 0;
   &.ended {
     background-color: #909399;
   }
   &.ended .status-bg-image {
     display: none;
   }
}

/* 步骤2: 让新加的 wrapper 容器可伸缩 */
.event-title-section {
  flex: 1; /* 关键：让这个容器占据剩余空间 */
  min-width: 0; /* 关键：一个flex布局的技巧，允许子元素在空间不足时正确换行 */
  margin-left: 16rpx;
}

/* 步骤3: 让内部的文字可以换行 */
.event-title {
  /* 关键：设置文本换行规则 */
  white-space: normal;
  word-break: break-word;
  
  /* 保留字体样式 */
  font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;
  font-weight: normal;
  font-size: 32rpx;
  color: #23232A;
  line-height: 1.5; /* 推荐：让多行文本有舒适的行间距 */
}

.info-row {
  display: flex;
  align-items: center;
  // margin-bottom: 16rpx; /* 行间距 */
  margin-top: 24rpx;
}

.info-text {
  /* 确保您已在项目中全局引入了该字体 */
  font-family: "Alibaba PuHuiTi 3.0", "Alibaba PuHuiTi 30", sans-serif;
  font-size: 26rpx; /* 更新字号 */
  color: #606266;   /* 这是基础文字颜色，蓝色会覆盖它 */
  margin-left: 16rpx; /* 图标与文字间距 */
}

.detail-section {
  background-color: #ffffff;
  margin: 30rpx 0; /* 与上方卡片和左右的间距 */
  // border-radius: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.05);
  padding: 30rpx;
}

.section-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.placeholder-content {
  /* 占位符容器样式 */
  background-color: #f8f8f8;
  border-radius: 10rpx;
  padding: 20rpx;
  text-align: center;
  margin-top: 20rpx;
}

.placeholder-text {
  /* 占位符文字样式 */
  color: #909399;
  font-size: 28rpx;
  line-height: 1.5;
}

.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  z-index: 999;
}

.rich-text-content {
  /* 富文本容器样式 */
  line-height: 1.6;
  font-size: 28rpx;
  color: #333333;
}

.rich-text {
  /* rich-text 组件样式 */
  width: 100%;
  line-height: 1.6;
}

/* rich-text 内部元素样式优化 */
:deep(.rich-text) {
  p {
    margin: 16rpx 0;
    line-height: 1.6;
  }
  
  img {
    max-width: 100%;
    height: auto;
    border-radius: 8rpx;
    margin: 16rpx 0;
  }
  
  h1, h2, h3, h4, h5, h6 {
    margin: 24rpx 0 16rpx 0;
    font-weight: bold;
  }
  
  ul, ol {
    margin: 16rpx 0;
    padding-left: 40rpx;
  }
  
  li {
    margin: 8rpx 0;
  }
  
  blockquote {
    background-color: #f8f8f8;
    border-left: 8rpx solid #409eff;
    padding: 16rpx;
    margin: 16rpx 0;
    border-radius: 8rpx;
  }
}

.summary-content {
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 10rpx;
}

.summary-text {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
}

.bottom-action-bar {
  position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%; /* 750rpx 即为全屏宽度 */
    box-sizing: border-box;
    z-index: 100;
  
    /* 关键: 应用您提供的所有样式 */
    height: calc(156rpx + env(safe-area-inset-bottom)); /* 容器总高度 = 设计高度 + 安全区域 */
    background-color: #FFFFFF ;
    border-top: 2rpx solid #EEEEEE; /* 设计稿中的 border，通常是顶部边框 */
    border-radius: 0;
    box-shadow: none; /* 移除旧阴影 */
    display: flex;
    justify-content: space-between;
    align-items: center; /* 垂直居中按钮 */
    /* 关键: 设置左右内边距，并为底部安全区域留出空间 */
    padding: 0 30rpx; 
    padding-bottom: env(safe-area-inset-bottom);
}

/* 新增：背景图样式 */
.status-bg-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1; /* 将图片置于底层 */
}

/* 新增：文字样式 */
.status-text {
    font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;
    font-weight: normal;
    font-size: 22rpx;
    color: #023F98; 
    position: relative;
    z-index: 2;
}

/* up-button 样式调整 */
:deep(.up-button--square) {
  border-radius: 10rpx !important; /* 确保按钮圆角 */
}
:deep(.up-button--primary) {
  border-radius: 44rpx !important; /* 立即报名按钮的圆角 */
}
</style>