package com.hongda.platform.domain;

import com.hongda.common.annotation.Excel;
import com.hongda.common.annotation.OssUrl;
import com.hongda.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.data.annotation.Transient;

/**
 * 导航配置对象 hongda_nav
 * * <AUTHOR>
 * @date 2025-07-18
 */
@Getter
@Setter
@ToString
@Schema(description = "导航配置")
public class HongdaNav extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 导航ID */
    @Schema(description = "导航ID", example = "1")
    private Long id;

    /** 导航标题 */
    @Excel(name = "导航标题")
    @Schema(description = "导航标题", example = "活动报名")
    private String title;

    /** 图标链接 */
    @Schema(description = "图标链接", example = "https://example.com/icon.png")
    @OssUrl
    private String iconUrl;

    /** 关联的内容页面ID */
    @Excel(name = "关联页面ID")
    @Schema(description = "关联的内容页面ID", example = "101")
    private Integer pageId;

    /** 导航位置代码 (关联字典 hongda_nav_position) */
    @Excel(name = "导航位置代码")
    @Schema(description = "导航位置代码", example = "HOME_NAV")
    private String positionCode;

    /** 排序 */
    @Excel(name = "排序")
    @Schema(description = "排序", example = "1")
    private Long sortOrder;

    /** 状态 */
    @Excel(name = "状态")
    @Schema(description = "状态", example = "1", allowableValues = {"0", "1"})
    private Integer status;

    /**
     * 关联的页面标题 (非数据表字段)
     * 使用 @Transient 注解标记，以明确它不参与持久化
     */
    @Schema(description = "关联的页面标题 (仅用于列表展示)")
    @Transient
    private String pageTitle;
}