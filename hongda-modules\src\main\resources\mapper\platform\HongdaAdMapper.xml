<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongda.platform.mapper.HongdaAdMapper">

    <resultMap type="com.hongda.platform.domain.HongdaAd" id="HongdaAdResult">
        <result property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="positionCode" column="position_code"/>
        <result property="imageUrl" column="image_url"/>
        <result property="pageId" column="page_id"/>
        <result property="linkUrl" column="link_url"/>
        <result property="relatedEventId" column="related_event_id"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="pageTitle" column="page_title"/>
        <result property="pageTargetUrl" column="page_target_url"/>
        <result property="pageType" column="page_type"/>
    </resultMap>

    <resultMap type="com.hongda.wxapp.domain.vo.AdVO" id="EventPromoAdResult">
        <result property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="positionCode" column="position_code"/>
        <result property="imageUrl" column="image_url"/>
        <result property="iconUrl" column="icon_url"/>
        <result property="linkUrl" column="link_url"/>
        <result property="relatedEventId" column="related_event_id"/>
        <result property="eventSummary" column="event_summary"/>
        <result property="eventSellPoint" column="event_sell_point"/>
        <result property="finalLinkUrl" column="final_link_url"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="status" column="status"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
    </resultMap>

    <sql id="selectHongdaAdVo">
        select id,
               title,
               position_code,
               image_url,
               page_id,
               link_url,
               related_event_id,
               start_time,
               end_time,
               sort_order,
               status,
               create_by,
               create_time,
               update_by,
               update_time
        from hongda_ad
    </sql>

    <select id="selectHongdaAdList" parameterType="HongdaAd" resultMap="HongdaAdResult">
        SELECT
        a.id,
        a.title,
        a.image_url,
        a.position_code,
        a.page_id,
        a.link_url,
        a.sort_order,
        a.status,
        a.create_time,
        p.title AS page_title,
        p.target_url AS page_target_url,
        p.page_type AS page_type -- ««« [新增] 查询关联页面的类型
        FROM
        hongda_ad a
        LEFT JOIN
        hongda_page p ON a.page_id = p.id
        <where>
            <if test="title != null  and title != ''">and a.title like concat('%', #{title}, '%')</if>
            <if test="positionCode != null  and positionCode != ''">and a.position_code = #{positionCode}</if>
            <if test="status != null ">and a.status = #{status}</if>
        </where>
    </select>

    <select id="selectEventPromoAdList" parameterType="String" resultMap="EventPromoAdResult">
        SELECT ad.id,
               ad.title,
               ad.position_code,
               ad.image_url,
               event.icon_url,
               ad.link_url,
               ad.related_event_id,
               event.summary                                          AS event_summary,
               event.sell_point                                       AS event_sell_point,
               CONCAT('/pages/event/detail?id=', ad.related_event_id) AS final_link_url,
               ad.sort_order,
               ad.status,
               ad.start_time,
               ad.end_time
        FROM hongda_ad AS ad
                 LEFT JOIN
             hongda_event AS event ON ad.related_event_id = event.id
        WHERE ad.position_code = #{positionCode}
          AND ad.status = 1
          AND ad.related_event_id IS NOT NULL
        ORDER BY ad.sort_order ASC
    </select>

    <select id="selectHongdaAdById" parameterType="Long" resultMap="HongdaAdResult">
        <include refid="selectHongdaAdVo"/>
        where id = #{id}
    </select>

    <insert id="insertHongdaAd" parameterType="com.hongda.platform.domain.HongdaAd" useGeneratedKeys="true"
            keyProperty="id">
        insert into hongda_ad
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="positionCode != null and positionCode != ''">position_code,</if>
            <if test="imageUrl != null and imageUrl != ''">image_url,</if>
            <if test="pageId != null">page_id,</if>
            <if test="linkUrl != null">link_url,</if>
            <if test="relatedEventId != null">related_event_id,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="positionCode != null and positionCode != ''">#{positionCode},</if>
            <if test="imageUrl != null and imageUrl != ''">#{imageUrl},</if>
            <if test="pageId != null">#{pageId},</if>
            <if test="linkUrl != null">#{linkUrl},</if>
            <if test="relatedEventId != null">#{relatedEventId},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateHongdaAd" parameterType="com.hongda.platform.domain.HongdaAd">
        update hongda_ad
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="positionCode != null and positionCode != ''">position_code = #{positionCode},</if>
            <if test="imageUrl != null and imageUrl != ''">image_url = #{imageUrl},</if>

            page_id = #{pageId},

            link_url = #{linkUrl},

            <if test="relatedEventId != null">related_event_id = #{relatedEventId},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHongdaAdById" parameterType="Long">
        delete
        from hongda_ad
        where id = #{id}
    </delete>


    <delete id="deleteHongdaAdByIds" parameterType="String">
        delete from hongda_ad where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据活动ID删除广告 -->
    <delete id="deleteAdByEventId" parameterType="Long">
        delete
        from hongda_ad
        where related_event_id = #{eventId}
    </delete>

    <!-- 根据活动ID查询广告 -->
    <select id="selectAdByEventId" parameterType="Long" resultMap="HongdaAdResult">
        <include refid="selectHongdaAdVo"/>
        where related_event_id = #{eventId}
    </select>

</mapper>