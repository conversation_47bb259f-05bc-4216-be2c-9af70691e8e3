package com.hongda.wxapp.domain.vo;

import com.hongda.common.annotation.OssUrl;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 广告VO对象，用于小程序端返回
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Schema(description = "广告信息VO")
@Getter
@Setter
public class AdVO {

    /** 广告ID */
    @Schema(description = "广告ID")
    private Long id;

    /** 广告标题 */
    @Schema(description = "广告标题")
    private String title;

    /** 广告位代码 */
    @Schema(description = "广告位代码")
    private String positionCode;

    /** 广告图片链接 */
    @Schema(description = "广告图片链接")
    @OssUrl
    private String imageUrl;

    /** 图标链接（用于活动推广的左侧图标） */
    @Schema(description = "图标链接")
    @OssUrl
    private String iconUrl;

    /** 跳转链接 */
    @Schema(description = "原始外部跳转链接")
    private String linkUrl;

    /** 关联的活动ID */
    @Schema(description = "关联的活动ID")
    private Long relatedEventId;

    /** 活动简介（仅活动推广类型使用） */
    @Schema(description = "活动简介")
    private String eventSummary;

    /** 活动卖点（仅活动推广类型使用） */
    @Schema(description = "活动卖点")
    private String eventSellPoint;

    /** 最终跳转链接（根据关联活动生成） */
    @Schema(description = "最终跳转链接")
    private String finalLinkUrl;

    /** 排序 */
    @Schema(description = "排序")
    private Long sortOrder;

    /** 状态 */
    @Schema(description = "状态")
    private Integer status;

    /** 生效开始时间 */
    @Schema(description = "生效开始时间")
    private Date startTime;

    /** 生效结束时间 */
    @Schema(description = "生效结束时间")
    private Date endTime;

    // ========== 新增字段 ==========

    /** 链接类型 (INTERNAL_PAGE, TAB_PAGE, EXTERNAL_LINK) */
    @Schema(description = "链接类型 (INTERNAL_PAGE, TAB_PAGE, EXTERNAL_LINK)")
    private String linkType;

    /** 链接目标 */
    @Schema(description = "链接目标")
    private String linkTarget;
}