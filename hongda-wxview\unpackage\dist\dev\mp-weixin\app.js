"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
const api_platform_asset = require("./api/platform/asset.js");
const uni_modules_uviewPlus_index = require("./uni_modules/uview-plus/index.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/article/index.js";
  "./pages/event/index.js";
  "./pages/country/index.js";
  "./pages/profile/index.js";
  "./pages/webview/index.js";
  "./pages_sub/pages_article/detail.js";
  "./pages_sub/pages_event/detail.js";
  "./pages_sub/pages_event/registration.js";
  "./pages_sub/pages_country/detail.js";
  "./pages_sub/pages_country/policy_detail.js";
  "./pages_sub/pages_profile/orders.js";
  "./pages_sub/pages_profile/contact.js";
  "./pages_sub/pages_other/search.js";
  "./pages_sub/pages_other/park_detail.js";
  "./pages_sub/pages_other/login.js";
  "./pages_sub/pages_other/registration_detail.js";
  "./pages_sub/pages_other/policy.js";
  "./pages_sub/pages_other/custom_page.js";
}
const _sfc_main = {
  onLaunch: function() {
    common_vendor.index.__f__("log", "at App.vue:7", "App Launch");
    this.fetchAndCacheAssets();
    this.loadCdnFonts();
    this.loadIconFont();
  },
  onShow: function() {
    common_vendor.index.__f__("log", "at App.vue:15", "App Show");
  },
  onHide: function() {
    common_vendor.index.__f__("log", "at App.vue:18", "App Hide");
  },
  methods: {
    /**
     * 【新增】从CDN加载所有自定义字体
     * 由于后台系统只能上传文件，我们在此处直接指定稳定高效的jsDelivr CDN地址
     */
    loadCdnFonts() {
      const fontsToLoad = [
        {
          family: "Alibaba PuHuiTi 3.0",
          // 阿里巴巴普惠体】jsDelivr CDN地址
          source: 'url("https://cdn.jsdelivr.net/gh/xiahezaixiamen/my-fonts/AlibabaPuHuiTi-3-55-Regular.woff2")'
        },
        {
          family: "YouSheBiaoTiHei",
          //优设标题黑】jsDelivr CDN地址
          source: 'url("https://cdn.jsdelivr.net/gh/xiahezaixiamen/my-fonts/YouSheBiaoTiHei.woff2")'
        }
      ];
      let loadedCount = 0;
      const totalFonts = fontsToLoad.length;
      fontsToLoad.forEach((font) => {
        common_vendor.index.loadFontFace({
          global: true,
          family: font.family,
          source: font.source,
          success: () => {
            common_vendor.index.__f__("log", "at App.vue:48", `CDN字体 [${font.family}] 加载成功!`);
            loadedCount++;
            if (loadedCount === totalFonts) {
              this.globalData = this.globalData || {};
              this.globalData.fontLoaded = true;
              common_vendor.index.$emit("customFontsLoaded");
              common_vendor.index.__f__("log", "at App.vue:59", "所有自定义字体加载完成，已触发全局事件");
            }
          },
          fail(err) {
            common_vendor.index.__f__("error", "at App.vue:63", `CDN字体 [${font.family}] 加载失败:`, err);
            loadedCount++;
            if (loadedCount === totalFonts) {
              this.globalData = this.globalData || {};
              this.globalData.fontLoaded = true;
              common_vendor.index.$emit("customFontsLoaded");
              common_vendor.index.__f__("log", "at App.vue:71", "字体加载处理完成（部分可能失败），已触发全局事件");
            }
          }
        });
      });
    },
    /**
     * 【保留并优化】获取、缓存静态资源（不再负责字体）
     * 此函数继续为App中的图片、背景等资源服务
     */
    async fetchAndCacheAssets() {
      try {
        const response = await api_platform_asset.getAllAssets();
        if (response.code === 200 && Array.isArray(response.data)) {
          const assetMap = response.data.reduce((map, item) => {
            if (item.assetKey && item.assetUrl) {
              map[item.assetKey] = item.assetUrl;
            }
            return map;
          }, {});
          common_vendor.index.setStorageSync("staticAssets", assetMap);
          common_vendor.index.__f__("log", "at App.vue:94", "小程序图片等静态资源已更新并缓存成功！");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at App.vue:98", "获取小程序静态资源失败", error);
      }
    },
    /**
     * 【已废弃】loadDynamicFonts 方法可以安全删除了，其功能已被 loadCdnFonts 替代
     */
    /**
     * 加载uView图标字体 (保留)
     */
    loadIconFont() {
      common_vendor.index.loadFontFace({
        global: true,
        family: "uicon-iconfont",
        source: 'url("https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf")',
        success() {
          common_vendor.index.__f__("log", "at App.vue:116", "uview-plus图标字体加载成功");
        },
        fail(err) {
          common_vendor.index.__f__("error", "at App.vue:119", "uview-plus图标字体加载失败:", err);
          common_vendor.index.loadFontFace({
            global: true,
            family: "uicon-iconfont",
            source: 'url("https://cdn.jsdelivr.net/npm/uview-plus@3.1.37/fonts/uicon-iconfont.ttf")',
            success() {
              common_vendor.index.__f__("log", "at App.vue:125", "备用图标字体加载成功");
            },
            fail(err2) {
              common_vendor.index.__f__("error", "at App.vue:128", "备用图标字体也加载失败:", err2);
            }
          });
        }
      });
    }
  }
};
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  app.use(uni_modules_uviewPlus_index.uviewPlus, () => {
    return {
      options: {
        config: {
          unit: "rpx"
        },
        props: {
          // ...
        }
      }
    };
  });
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
