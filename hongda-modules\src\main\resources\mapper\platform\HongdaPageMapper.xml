<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongda.platform.mapper.HongdaPageMapper">
    
    <resultMap type="HongdaPage" id="HongdaPageResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="pageType"    column="page_type"    />
        <result property="content"    column="content"    />
        <result property="targetUrl"    column="target_url"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectHongdaPageVo">
        select id, title, page_type, content, target_url, sort_order, status, remark, create_by, create_time, update_by, update_time from hongda_page
    </sql>

    <select id="selectHongdaPageList" parameterType="HongdaPage" resultMap="HongdaPageResult">
        <include refid="selectHongdaPageVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="pageType != null  and pageType != ''"> and page_type = #{pageType}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectHongdaPageById" parameterType="Long" resultMap="HongdaPageResult">
        <include refid="selectHongdaPageVo"/>
        where id = #{id}
    </select>

    <insert id="insertHongdaPage" parameterType="HongdaPage" useGeneratedKeys="true" keyProperty="id">
        insert into hongda_page
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="pageType != null and pageType != ''">page_type,</if>
            <if test="content != null">content,</if>
            <if test="targetUrl != null">target_url,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="pageType != null and pageType != ''">#{pageType},</if>
            <if test="content != null">#{content},</if>
            <if test="targetUrl != null">#{targetUrl},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateHongdaPage" parameterType="HongdaPage">
        update hongda_page
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="pageType != null and pageType != ''">page_type = #{pageType},</if>
            <if test="content != null">content = #{content},</if>
            <if test="targetUrl != null">target_url = #{targetUrl},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHongdaPageById" parameterType="Long">
        delete from hongda_page where id = #{id}
    </delete>

    <delete id="deleteHongdaPageByIds" parameterType="String">
        delete from hongda_page where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>