<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongda.platform.mapper.HongdaNavMapper">

    <resultMap type="com.hongda.platform.domain.HongdaNav" id="HongdaNavResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="iconUrl"    column="icon_url"    />
        <result property="pageId"    column="page_id"    />
        <result property="positionCode"    column="position_code"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="pageTitle" column="page_title" />
    </resultMap>

    <sql id="selectHongdaNavVo">
        select id, title, icon_url, page_id, position_code, sort_order, status, create_by, create_time, update_by, update_time from hongda_nav
    </sql>

    <select id="selectHongdaNavList" parameterType="com.hongda.platform.domain.HongdaNav" resultMap="HongdaNavResult">
        select
        n.id,
        n.title,
        n.icon_url,
        n.page_id,
        n.position_code,
        n.sort_order,
        n.status,
        n.create_by,
        n.create_time,
        n.update_by,
        n.update_time,
        p.title as page_title
        from hongda_nav n
        left join hongda_page p on n.page_id = p.id
        <where>
            <if test="title != null  and title != ''"> and n.title like concat('%', #{title}, '%')</if>
            <if test="pageId != null "> and n.page_id = #{pageId}</if>
            <if test="positionCode != null  and positionCode != ''"> and n.position_code = #{positionCode}</if>
            <if test="status != null "> and n.status = #{status}</if>
        </where>
        order by n.sort_order asc
    </select>

    <select id="selectHongdaNavById" parameterType="Long" resultMap="HongdaNavResult">
        <include refid="selectHongdaNavVo"/>
        where id = #{id}
    </select>

    <insert id="insertHongdaNav" parameterType="com.hongda.platform.domain.HongdaNav" useGeneratedKeys="true" keyProperty="id">
        insert into hongda_nav
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="iconUrl != null and iconUrl != ''">icon_url,</if>
            <if test="pageId != null">page_id,</if>
            <if test="positionCode != null and positionCode != ''">position_code,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="iconUrl != null and iconUrl != ''">#{iconUrl},</if>
            <if test="pageId != null">#{pageId},</if>
            <if test="positionCode != null and positionCode != ''">#{positionCode},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateHongdaNav" parameterType="com.hongda.platform.domain.HongdaNav">
        update hongda_nav
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="iconUrl != null and iconUrl != ''">icon_url = #{iconUrl},</if>
            <if test="pageId != null">page_id = #{pageId},</if>
            <if test="positionCode != null and positionCode != ''">position_code = #{positionCode},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHongdaNavById" parameterType="Long">
        delete from hongda_nav where id = #{id}
    </delete>

    <delete id="deleteHongdaNavByIds" parameterType="String">
        delete from hongda_nav where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>