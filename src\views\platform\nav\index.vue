<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="导航标题" prop="title">
        <el-input
            v-model="queryParams.title"
            placeholder="请输入导航标题"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="导航位置" prop="positionCode">
        <el-select v-model="queryParams.positionCode" placeholder="请选择导航位置" clearable style="width: 200px">
          <el-option
              v-for="dict in hongda_nav_position"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 150px">
          <el-option
              v-for="dict in hongda_common_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
    </el-row>

    <el-table v-loading="loading" :data="navList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="导航ID" align="center" prop="id" />
      <el-table-column label="导航标题" align="center" prop="title" />
      <el-table-column label="图标" align="center" width="100">
        <template #default="scope">
          <el-image
              v-if="scope.row.iconUrl"
              style="width: 50px; height: 50px; border-radius: 5px;"
              :src="scope.row.iconUrl"
              :preview-src-list="[scope.row.iconUrl]"
              :initial-index="0"
              fit="cover"
              preview-teleported
          />
          <span v-else>无</span>
        </template>
      </el-table-column>
      <el-table-column label="关联页面" align="center" prop="pageTitle" />
      <el-table-column label="导航位置" align="center" prop="positionCode">
        <template #default="scope">
          <dict-tag :options="hongda_nav_position" :value="scope.row.positionCode"/>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="sortOrder" />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="hongda_common_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
        <template #default="scope">
          <el-dropdown trigger="click">
            <el-button link type="primary">
              更多操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                    icon="Edit"
                    @click="handleUpdate(scope.row)"
                    v-hasPermi="['platform:nav:edit']">
                  修改
                </el-dropdown-item>
                <el-dropdown-item
                    icon="Delete"
                    @click="handleDelete(scope.row)"
                    v-hasPermi="['platform:nav:remove']"
                    :style="{ color: 'var(--el-color-danger)' }">
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <el-dialog :title="title" v-model="open" width="700px" append-to-body>
      <el-form ref="navRef" :model="form" :rules="rules" label-width="150px">
        <el-form-item label="导航标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入导航标题" />
        </el-form-item>
        <el-form-item label="图标链接" prop="iconUrl">
          <image-upload v-model="form.iconUrl" :limit="1" />
        </el-form-item>

        <el-form-item label="关联页面" prop="pageId">
          <el-select
              v-model="form.pageId"
              placeholder="请搜索并选择页面"
              filterable
              clearable
              style="width: 100%"
          >
            <el-option
                v-for="item in pageOptions"
                :key="item.id"
                :label="item.title"
                :value="item.id"
            >
              <span style="float: left">{{ item.title }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.id }}</span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="导航位置" prop="positionCode">
          <el-select v-model="form.positionCode" placeholder="请选择导航位置">
            <el-option
                v-for="dict in hongda_nav_position"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="form.sortOrder" controls-position="right" :min="0" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
                v-for="dict in hongda_common_status"
                :key="dict.value"
                :label="parseInt(dict.value)"
            >{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Nav">
import { listNav, getNav, delNav, addNav, updateNav } from "@/api/platform/nav";
// 步骤 1: 引入新的 page API
import { listPage } from "@/api/platform/page";

const { proxy } = getCurrentInstance();
// 步骤 2: 移除不再需要的 hongda_nav_link_type 字典
const { hongda_common_status, hongda_nav_position } = proxy.useDict('hongda_common_status', 'hongda_nav_position');

const navList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 步骤 3: 新增一个 ref 用于存放页面选择器的选项
const pageOptions = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    title: null,
    // linkType 和 linkTarget 已移除
    positionCode: null,
    status: null,
  },
  rules: {
    title: [
      { required: true, message: "导航标题不能为空", trigger: "blur" }
    ],
    iconUrl: [
      { required: true, message: "图标链接不能为空", trigger: "blur" }
    ],
    // 步骤 4: 将旧规则替换为 pageId 的新规则
    pageId: [
      { required: true, message: "关联页面不能为空", trigger: "change" }
    ],
    positionCode: [
      { required: true, message: "导航位置代码不能为空", trigger: "change" }
    ],
    sortOrder: [
      { required: true, message: "排序不能为空", trigger: "blur" }
    ],
    status: [
      { required: true, message: "状态不能为空", trigger: "change" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 步骤 5: 新增方法，用于获取所有页面数据 */
function getPageList() {
  listPage({ pageNum: 1, pageSize: 9999 }).then(response => { // 请求所有页面数据
    pageOptions.value = response.rows;
  });
}

/** 查询导航配置列表 */
function getList() {
  loading.value = true;
  listNav(queryParams.value).then(response => {
    navList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    title: null,
    iconUrl: null,
    // 步骤 6: 更新表单模型，使用 pageId
    pageId: null,
    positionCode: null,
    sortOrder: null,
    status: null,
  };
  proxy.resetForm("navRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  getPageList(); // 在打开弹窗前获取页面列表
  open.value = true;
  title.value = "添加导航配置";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  getPageList(); // 在打开弹窗前获取页面列表
  const _id = row.id || ids.value;
  getNav(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改导航配置";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["navRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateNav(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addNav(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除导航配置编号为"' + _ids + '"的数据项？').then(function() {
    return delNav(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('platform/nav/export', {
    ...queryParams.value
  }, `nav_${new Date().getTime()}.xlsx`);
}

getList();
</script>
