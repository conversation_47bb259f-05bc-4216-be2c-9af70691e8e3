"use strict";
const common_vendor = require("../../common/vendor.js");
const api_data_user = require("../../api/data/user.js");
const utils_config = require("../../utils/config.js");
if (!Array) {
  const _easycom_up_avatar2 = common_vendor.resolveComponent("up-avatar");
  const _easycom_up_cell2 = common_vendor.resolveComponent("up-cell");
  const _easycom_up_cell_group2 = common_vendor.resolveComponent("up-cell-group");
  (_easycom_up_avatar2 + _easycom_up_cell2 + _easycom_up_cell_group2)();
}
const _easycom_up_avatar = () => "../../uni_modules/uview-plus/components/u-avatar/u-avatar.js";
const _easycom_up_cell = () => "../../uni_modules/uview-plus/components/u-cell/u-cell.js";
const _easycom_up_cell_group = () => "../../uni_modules/uview-plus/components/u-cell-group/u-cell-group.js";
if (!Math) {
  (_easycom_up_avatar + _easycom_up_cell + _easycom_up_cell_group + CustomTabBar)();
}
const CustomTabBar = () => "../../components/layout/CustomTabBar.js";
const MAX_NICKNAME_LENGTH = 15;
const _sfc_main = /* @__PURE__ */ Object.assign({
  name: "ProfileIndex"
}, {
  __name: "index",
  setup(__props) {
    const isLoggedIn = common_vendor.ref(false);
    const userInfo = common_vendor.ref(null);
    const defaultAvatarUrl = common_vendor.ref("");
    const headerBgUrl = common_vendor.ref("");
    const editIconUrl = common_vendor.ref("");
    const orderArrowUrl = common_vendor.ref("");
    const phoneIconUrl = common_vendor.ref("");
    const contractIconUrl = common_vendor.ref("");
    const privacyIconUrl = common_vendor.ref("");
    const deleteIconUrl = common_vendor.ref("");
    const orderCardBgUrl = common_vendor.ref("");
    const resolveAssetUrl = (assetKey) => {
      const assets = common_vendor.index.getStorageSync("staticAssets");
      return assets && assets[assetKey] ? assets[assetKey] : "";
    };
    const refreshDefaultAvatar = () => {
      defaultAvatarUrl.value = resolveAssetUrl("default_avatar");
    };
    const refreshProfileAssets = () => {
      headerBgUrl.value = resolveAssetUrl("mybg");
      editIconUrl.value = resolveAssetUrl("my_edit");
      orderArrowUrl.value = resolveAssetUrl("group_right");
      phoneIconUrl.value = resolveAssetUrl("my_phone");
      contractIconUrl.value = resolveAssetUrl("my_contract");
      privacyIconUrl.value = resolveAssetUrl("my_personal");
      deleteIconUrl.value = resolveAssetUrl("my_delete");
      orderCardBgUrl.value = resolveAssetUrl("order-card-bg");
    };
    const getUserDisplayName = common_vendor.computed(() => {
      if (!userInfo.value) {
        return "用户";
      }
      if (userInfo.value.nickname) {
        return userInfo.value.nickname;
      }
      if (userInfo.value.phoneNumber) {
        const phone = userInfo.value.phoneNumber;
        if (phone.length === 11) {
          return phone.substring(0, 3) + "****" + phone.substring(7);
        }
        return phone;
      }
      return "用户";
    });
    const getPhoneDisplay = common_vendor.computed(() => {
      var _a, _b;
      if (!isLoggedIn.value) {
        return "";
      }
      const phone = ((_a = userInfo.value) == null ? void 0 : _a.phone) || ((_b = userInfo.value) == null ? void 0 : _b.phoneNumber);
      if (!phone) {
        return "未绑定";
      }
      if (phone.length === 11) {
        return phone.substring(0, 3) + "****" + phone.substring(7);
      }
      return phone;
    });
    const handleNavigate = (url) => {
      if (!isLoggedIn.value) {
        common_vendor.index.__f__("log", "at pages/profile/index.vue:192", "用户未登录，跳转到登录页");
        goToLogin();
        return;
      }
      common_vendor.index.__f__("log", "at pages/profile/index.vue:198", "用户已登录，跳转到:", url);
      common_vendor.index.navigateTo({
        url,
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/profile/index.vue:202", "页面跳转失败:", err);
          common_vendor.index.showToast({
            title: "页面跳转失败",
            icon: "none",
            duration: 2e3
          });
        }
      });
    };
    const handleOrderCardClick = () => {
      if (!isLoggedIn.value) {
        common_vendor.index.__f__("log", "at pages/profile/index.vue:215", "点击报名订单卡片，需要先登录");
        goToLogin();
      } else {
        common_vendor.index.__f__("log", "at pages/profile/index.vue:218", "点击报名订单卡片，跳转到订单页面");
        common_vendor.index.navigateTo({
          url: "/pages_sub/pages_profile/orders",
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/profile/index.vue:222", "跳转订单页面失败:", err);
            common_vendor.index.showToast({
              title: "页面跳转失败",
              icon: "none",
              duration: 2e3
            });
          }
        });
      }
    };
    const handleDeleteAccountClick = () => {
      if (!isLoggedIn.value) {
        common_vendor.index.__f__("log", "at pages/profile/index.vue:237", "注销账号需要先登录");
        goToLogin();
        return;
      }
      common_vendor.index.showModal({
        title: "注销账号",
        content: "注销后账号将被标记为已注销，确定要继续吗？",
        confirmText: "确定注销",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.__f__("log", "at pages/profile/index.vue:251", "用户确认注销账号");
            confirmDeleteAccount();
          }
        }
      });
    };
    const confirmDeleteAccount = async () => {
      common_vendor.index.__f__("log", "at pages/profile/index.vue:260", "=== 开始执行注销账号流程 ===");
      common_vendor.index.showLoading({ title: "正在注销..." });
      try {
        common_vendor.index.__f__("log", "at pages/profile/index.vue:266", "调用后端注销接口...");
        const result = await api_data_user.deleteAccountApi();
        common_vendor.index.__f__("log", "at pages/profile/index.vue:269", "注销接口调用成功:", result);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "账号已成功注销",
          icon: "success",
          duration: 2e3
        });
        clearUserDataSilently();
        common_vendor.index.__f__("log", "at pages/profile/index.vue:284", "注销账号流程完成");
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/profile/index.vue:287", "注销账号过程中发生错误:", error);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "注销失败，请稍后再试",
          icon: "none",
          duration: 3e3
        });
        common_vendor.index.__f__("error", "at pages/profile/index.vue:300", "注销失败详情:", {
          message: error.message,
          stack: error.stack
        });
      }
    };
    const checkLoginStatus = () => {
      const token = common_vendor.index.getStorageSync("token");
      const userInfoData = common_vendor.index.getStorageSync("userInfo");
      common_vendor.index.__f__("log", "at pages/profile/index.vue:313", "从本地存储获取的token:", token);
      common_vendor.index.__f__("log", "at pages/profile/index.vue:314", "从本地存储获取的userInfo:", userInfoData);
      const newLoginStatus = !!token;
      common_vendor.index.__f__("log", "at pages/profile/index.vue:317", "计算出的登录状态:", newLoginStatus);
      common_vendor.index.__f__("log", "at pages/profile/index.vue:318", "当前页面登录状态:", isLoggedIn.value);
      isLoggedIn.value = newLoginStatus;
      userInfo.value = userInfoData || null;
      if (isLoggedIn.value && userInfoData) {
        common_vendor.index.__f__("log", "at pages/profile/index.vue:326", "用户已登录，用户信息:", userInfoData);
        if (userInfoData.phoneNumber) {
          common_vendor.index.__f__("log", "at pages/profile/index.vue:328", "用户手机号:", userInfoData.phoneNumber);
        }
      } else if (isLoggedIn.value && !userInfoData) {
        common_vendor.index.__f__("log", "at pages/profile/index.vue:331", "有token但无用户信息");
      } else {
        common_vendor.index.__f__("log", "at pages/profile/index.vue:333", "用户未登录");
      }
    };
    const goToLogin = () => {
      common_vendor.index.navigateTo({
        url: "/pages_sub/pages_other/login"
      });
    };
    const logout = (isDeleteAccount = false) => {
      if (isDeleteAccount) {
        clearUserData("已退出登录");
        return;
      }
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要退出登录吗？",
        success: (res) => {
          if (res.confirm) {
            clearUserData("已退出登录");
          }
        }
      });
    };
    const handleEditNickname = () => {
      var _a;
      if (!isLoggedIn.value) {
        common_vendor.index.__f__("log", "at pages/profile/index.vue:367", "编辑昵称需要先登录");
        goToLogin();
        return;
      }
      common_vendor.index.showModal({
        title: "修改昵称",
        editable: true,
        placeholderText: "请输入新昵称",
        content: ((_a = userInfo.value) == null ? void 0 : _a.nickname) || "",
        success: async (res) => {
          if (res.confirm && res.content && res.content.trim()) {
            const newNickname = res.content.trim();
            if (newNickname.length > MAX_NICKNAME_LENGTH) {
              common_vendor.index.showToast({
                title: `昵称不能超过${MAX_NICKNAME_LENGTH}个字符`,
                icon: "none",
                duration: 2e3
              });
              return;
            }
            await updateNickname(newNickname);
          }
        }
      });
    };
    const updateNickname = async (newNickname) => {
      common_vendor.index.__f__("log", "at pages/profile/index.vue:401", "=== 开始更新昵称 ===");
      if (!newNickname || newNickname.trim().length === 0) {
        common_vendor.index.showToast({
          title: "昵称不能为空",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      if (newNickname.length > MAX_NICKNAME_LENGTH) {
        common_vendor.index.showToast({
          title: `昵称不能超过${MAX_NICKNAME_LENGTH}个字符`,
          icon: "none",
          duration: 2e3
        });
        return;
      }
      common_vendor.index.showLoading({ title: "正在更新..." });
      try {
        common_vendor.index.__f__("log", "at pages/profile/index.vue:425", "调用updateUserInfoApi更新昵称:", newNickname);
        const result = await api_data_user.updateUserInfoApi({
          nickname: newNickname
        });
        common_vendor.index.__f__("log", "at pages/profile/index.vue:430", "昵称更新接口调用成功:", result);
        common_vendor.index.hideLoading();
        if (userInfo.value) {
          userInfo.value.nickname = newNickname;
          common_vendor.index.setStorageSync("userInfo", userInfo.value);
        }
        common_vendor.index.showToast({
          title: "昵称修改成功",
          icon: "success",
          duration: 2e3
        });
        common_vendor.index.__f__("log", "at pages/profile/index.vue:449", "昵称更新完成");
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/profile/index.vue:452", "更新昵称过程中发生错误:", error);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: error.message || "更新失败，请稍后再试",
          icon: "none",
          duration: 3e3
        });
        common_vendor.index.__f__("error", "at pages/profile/index.vue:464", "昵称更新失败详情:", {
          message: error.message,
          stack: error.stack
        });
      }
    };
    const clearUserData = (message) => {
      common_vendor.index.__f__("log", "at pages/profile/index.vue:473", "=== 开始清除用户数据 ===");
      common_vendor.index.removeStorageSync("token");
      common_vendor.index.removeStorageSync("userInfo");
      isLoggedIn.value = false;
      userInfo.value = null;
      if (message) {
        common_vendor.index.showToast({
          title: message,
          icon: "success",
          duration: 1500
        });
      }
      common_vendor.index.__f__("log", "at pages/profile/index.vue:492", "用户数据已清除，UI已更新");
    };
    const clearUserDataSilently = () => {
      common_vendor.index.__f__("log", "at pages/profile/index.vue:497", "=== 静默清除用户数据 ===");
      common_vendor.index.removeStorageSync("token");
      common_vendor.index.removeStorageSync("userInfo");
      isLoggedIn.value = false;
      userInfo.value = null;
      common_vendor.index.__f__("log", "at pages/profile/index.vue:507", "用户数据已静默清除，UI已更新");
    };
    common_vendor.onShow(() => {
      common_vendor.index.hideTabBar();
      setTimeout(() => {
        checkLoginStatus();
        refreshDefaultAvatar();
        refreshProfileAssets();
      }, 100);
    });
    common_vendor.onLoad(() => {
      checkLoginStatus();
      refreshDefaultAvatar();
      refreshProfileAssets();
    });
    const onChooseAvatar = (e) => {
      var _a;
      const tempPath = (_a = e == null ? void 0 : e.detail) == null ? void 0 : _a.avatarUrl;
      if (!tempPath)
        return;
      uploadAvatar(tempPath);
    };
    const uploadAvatar = async (filePath) => {
      if (!isLoggedIn.value) {
        goToLogin();
        return;
      }
      const token = common_vendor.index.getStorageSync("token");
      if (!token) {
        goToLogin();
        return;
      }
      common_vendor.index.showLoading({ title: "上传中..." });
      try {
        await new Promise((resolve, reject) => {
          common_vendor.index.uploadFile({
            url: utils_config.BASE_URL + "/common/upload",
            filePath,
            name: "file",
            header: {
              Authorization: "Bearer " + token
            },
            success: async (res) => {
              try {
                const data = JSON.parse(res.data || "{}");
                if (res.statusCode === 200 && data.code === 200 && data.url) {
                  const url = data.url;
                  await api_data_user.updateUserInfoApi({ avatarUrl: url });
                  if (userInfo.value) {
                    userInfo.value.avatarUrl = url;
                    common_vendor.index.setStorageSync("userInfo", userInfo.value);
                  }
                  common_vendor.index.showToast({ title: "头像已更新", icon: "success" });
                  resolve(true);
                } else {
                  common_vendor.index.showToast({ title: "上传失败", icon: "none" });
                  reject(new Error("upload error"));
                }
              } catch (err) {
                common_vendor.index.showToast({ title: "响应解析失败", icon: "none" });
                reject(err);
              }
            },
            fail: (err) => {
              common_vendor.index.showToast({ title: "上传失败", icon: "none" });
              reject(err);
            },
            complete: () => {
              common_vendor.index.hideLoading();
            }
          });
        });
      } catch (e) {
      }
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: headerBgUrl.value,
        b: common_vendor.o(($event) => !isLoggedIn.value ? goToLogin : null),
        c: common_vendor.p({
          size: 54,
          src: isLoggedIn.value && userInfo.value && userInfo.value.avatarUrl ? userInfo.value.avatarUrl : defaultAvatarUrl.value
        }),
        d: isLoggedIn.value
      }, isLoggedIn.value ? {
        e: common_vendor.o(onChooseAvatar)
      } : {}, {
        f: isLoggedIn.value
      }, isLoggedIn.value ? {
        g: common_vendor.t(getUserDisplayName.value),
        h: editIconUrl.value,
        i: common_vendor.o(handleEditNickname)
      } : {
        j: common_vendor.o(goToLogin)
      }, {
        k: orderCardBgUrl.value,
        l: orderArrowUrl.value,
        m: common_vendor.o(handleOrderCardClick),
        n: phoneIconUrl.value,
        o: common_vendor.p({
          title: "绑定手机号",
          value: getPhoneDisplay.value,
          isLink: false,
          border: false
        }),
        p: privacyIconUrl.value,
        q: common_vendor.o(($event) => handleNavigate("/pages_sub/pages_other/policy?type=privacy_policy")),
        r: common_vendor.p({
          title: "隐私政策",
          isLink: true,
          ["arrow-direction"]: "right",
          border: false
        }),
        s: contractIconUrl.value,
        t: common_vendor.o(($event) => handleNavigate("/pages_sub/pages_other/policy?type=user_agreement")),
        v: common_vendor.p({
          title: "用户协议",
          isLink: true,
          ["arrow-direction"]: "right",
          border: false
        }),
        w: deleteIconUrl.value,
        x: common_vendor.o(handleDeleteAccountClick),
        y: common_vendor.p({
          title: "注销账号",
          isLink: true,
          ["arrow-direction"]: "right",
          border: false
        }),
        z: common_vendor.p({
          border: false
        }),
        A: isLoggedIn.value
      }, isLoggedIn.value ? {
        B: common_vendor.o(logout)
      } : {}, {
        C: common_vendor.p({
          current: 4
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-201c0da5"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/profile/index.js.map
