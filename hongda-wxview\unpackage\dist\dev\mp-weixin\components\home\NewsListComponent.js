"use strict";
const common_vendor = require("../../common/vendor.js");
const api_content_article = require("../../api/content/article.js");
const api_content_tag = require("../../api/content/tag.js");
const utils_image = require("../../utils/image.js");
if (!Array) {
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_tabs2 = common_vendor.resolveComponent("u-tabs");
  const _easycom_u_image2 = common_vendor.resolveComponent("u-image");
  (_easycom_u_icon2 + _easycom_u_tabs2 + _easycom_u_image2)();
}
const _easycom_u_icon = () => "../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_u_tabs = () => "../../uni_modules/uview-plus/components/u-tabs/u-tabs.js";
const _easycom_u_image = () => "../../uni_modules/uview-plus/components/u-image/u-image.js";
if (!Math) {
  (_easycom_u_icon + _easycom_u_tabs + _easycom_u_image)();
}
const _sfc_main = {
  __name: "NewsListComponent",
  props: {
    title: {
      type: String,
      default: "为你推荐"
    },
    params: {
      type: Object,
      default: () => ({
        pageNum: 1,
        pageSize: 4,
        orderByColumn: "publish_time",
        isAsc: "desc"
      })
    }
  },
  setup(__props) {
    const props = __props;
    const articleList = common_vendor.ref([]);
    const tagList = common_vendor.ref([]);
    const currentTabIndex = common_vendor.ref(0);
    const queryParams = common_vendor.ref({ ...props.params });
    const titleParts = common_vendor.computed(() => {
      const title = props.title;
      if (title.length > 2) {
        return {
          main: title.slice(0, 2),
          gradient: title.slice(2)
        };
      }
      return { main: title, gradient: "" };
    });
    const loadTags = async () => {
      try {
        const response = await api_content_tag.listAllTag();
        const backendTags = Array.isArray(response.data) ? response.data : [];
        tagList.value = [{ id: null, name: "全部" }, ...backendTags.slice(0, 4)];
      } catch (error) {
        common_vendor.index.__f__("error", "at components/home/<USER>", "加载标签列表失败:", error);
      }
    };
    const loadArticles = async (isRefresh = false) => {
      if (isRefresh) {
        queryParams.value.pageNum = 1;
      }
      try {
        const response = await api_content_article.getArticleList(queryParams.value);
        articleList.value = response.rows;
      } catch (error) {
        common_vendor.index.__f__("error", "at components/home/<USER>", "加载资讯列表失败:", error);
      }
    };
    const handleTabChange = (tab) => {
      currentTabIndex.value = tab.index;
      queryParams.value.tagIds = tagList.value[tab.index].id;
      loadArticles(true);
    };
    const gotoDetail = (id) => {
      common_vendor.index.navigateTo({
        url: `/pages_sub/pages_article/detail?id=${id}`
      });
    };
    const goToArticleListPage = () => {
      common_vendor.index.switchTab({
        url: "/pages/article/index"
      });
    };
    const formatDate = (dateString) => {
      if (!dateString)
        return "";
      return dateString.split(" ")[0];
    };
    common_vendor.onMounted(() => {
      loadTags();
      loadArticles(true);
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: articleList.value.length > 0
      }, articleList.value.length > 0 ? {
        b: common_vendor.t(titleParts.value.main),
        c: common_vendor.t(titleParts.value.gradient),
        d: common_vendor.p({
          name: "arrow-right",
          size: "14",
          color: "#909399"
        }),
        e: common_vendor.o(goToArticleListPage),
        f: common_vendor.o(handleTabChange),
        g: common_vendor.p({
          list: tagList.value,
          current: currentTabIndex.value,
          keyName: "name",
          lineColor: "#023F98",
          lineHeight: 4,
          lineWidth: 40,
          activeStyle: {
            color: "#23232A",
            fontSize: "30rpx",
            fontWeight: "bold"
          },
          inactiveStyle: {
            color: "#9B9A9A",
            fontSize: "30rpx"
          }
        }),
        h: common_vendor.f(articleList.value, (item, k0, i0) => {
          return {
            a: "78e4d293-2-" + i0,
            b: common_vendor.p({
              src: common_vendor.unref(utils_image.getFullImageUrl)(item.coverImageUrl),
              width: "336rpx",
              height: "192rpx",
              radius: "12rpx",
              ["lazy-load"]: true
            }),
            c: common_vendor.t(item.title),
            d: common_vendor.t(item.source),
            e: common_vendor.t(formatDate(item.publishTime)),
            f: item.id,
            g: common_vendor.o(($event) => gotoDetail(item.id), item.id)
          };
        })
      } : {});
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-78e4d293"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/home/<USER>
