package com.hongda.wxapp.controller;

import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.platform.domain.HongdaAd;
import com.hongda.platform.mapper.HongdaAdMapper;
import com.hongda.platform.service.IHongdaAdService;
import com.hongda.wxapp.domain.vo.AdVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Tag(name = "小程序广告接口", description = "小程序端广告相关接口")
@RestController
@RequestMapping("/api/v1/ad")
public class WxAppAdController extends BaseController {

    @Autowired
    private IHongdaAdService hongdaAdService;

    // 注意：在Controller中直接注入Mapper通常不是最佳实践，但为了与您提供的代码保持一致，此处保留
    @Autowired
    private HongdaAdMapper hongdaAdMapper;

    private static final String HOME_PROMO_EVENT = "HOME_PROMO_EVENT";

    // 这个列表已根据您的 pages.json 文件精确更新，确保跳转类型判断正确
    private static final List<String> TAB_BAR_PAGES = Arrays.asList(
            "/pages/index/index",
            "/pages/article/index",
            "/pages/event/index",
            "/pages/country/index",
            "/pages/profile/index"
    );


    @Operation(summary = "获取广告列表", description = "根据位置代码获取启用状态的广告列表")
    @GetMapping("/list")
    public AjaxResult getAdListByPosition(
            @Parameter(description = "广告位置代码", example = "HOME_BANNER") @RequestParam("positionCode") String positionCode,
            @Parameter(description = "状态(0-禁用,1-启用)", example = "1") @RequestParam(value = "status", defaultValue = "1") Integer status,
            @Parameter(description = "页面大小", example = "10") @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        if (HOME_PROMO_EVENT.equals(positionCode)) {
            return handleEventPromoAd(positionCode);
        }
        return handleRegularAd(positionCode, status);
    }

    /**
     * 处理活动推广广告 (此部分逻辑保持不变)
     */
    private AjaxResult handleEventPromoAd(String positionCode) {
        try {
            List<AdVO> eventPromoAdList = hongdaAdMapper.selectEventPromoAdList(positionCode);
            Date now = new Date();
            List<AdVO> validAdList = eventPromoAdList.stream()
                    .filter(ad -> {
                        if (ad.getStartTime() != null && now.before(ad.getStartTime())) return false;
                        if (ad.getEndTime() != null && now.after(ad.getEndTime())) return false;
                        return true;
                    })
                    .collect(Collectors.toList());
            return AjaxResult.success(validAdList);
        } catch (Exception e) {
            System.err.println("查询活动推广广告失败: " + e.getMessage());
            e.printStackTrace();
            return AjaxResult.error("查询活动推广广告失败");
        }
    }

    /**
     * 处理常规广告 (包含所有修复逻辑)
     */
// com/hongda/wxapp/controller/WxAppAdController.java

    private AjaxResult handleRegularAd(String positionCode, Integer status) {
        // 1. 准备查询参数
        HongdaAd queryParams = new HongdaAd();
        queryParams.setPositionCode(positionCode);
        queryParams.setStatus(status);

        // 2. 调用Service层获取数据
        List<HongdaAd> adList = hongdaAdService.selectHongdaAdList(queryParams);

        // 3. 过滤有效期内的广告
        Date now = new Date();
        List<HongdaAd> validAdList = adList.stream()
                .filter(ad -> {
                    if (ad.getStartTime() != null && now.before(ad.getStartTime())) return false;
                    if (ad.getEndTime() != null && now.after(ad.getEndTime())) return false;
                    return true;
                })
                .collect(Collectors.toList());

        // 4. [核心修改] 转换为VO，并生成包含动态链接的 linkType 和 linkTarget
        List<AdVO> voList = validAdList.stream().map(ad -> {
            AdVO vo = new AdVO();
            BeanUtils.copyProperties(ad, vo);

            String finalLinkTarget = null; // 最终的跳转目标

            // --- 优先处理内部页面链接 ---
            if (ad.getPageId() != null) {
                // 如果页面类型是自定义页面 (RICH_TEXT)，则动态构建链接
                if ("RICH_TEXT".equals(ad.getPageType())) {
                    finalLinkTarget = "/pages_sub/pages_other/custom_page?id=" + ad.getPageId(); // ««« 修改后的正确路径
                } else {
                    // 否则，使用页面表中已有的 target_url
                    finalLinkTarget = ad.getPageTargetUrl();
                }

                // 如果最终获得了有效的内部链接
                if (finalLinkTarget != null && !finalLinkTarget.isEmpty()) {
                    // 判断是 TabBar 页面还是普通页面
                    if (TAB_BAR_PAGES.contains(finalLinkTarget.split("\\?")[0])) {
                        vo.setLinkType("TAB_PAGE");
                    } else {
                        vo.setLinkType("INTERNAL_PAGE");
                    }
                    vo.setLinkTarget(finalLinkTarget);
                }
            }
            // --- 如果没有内部页面，再处理外部链接 ---
            else if (ad.getLinkUrl() != null && !ad.getLinkUrl().isEmpty()) {
                vo.setLinkType("EXTERNAL_LINK");
                vo.setLinkTarget(ad.getLinkUrl());
            }

            return vo;
        }).collect(Collectors.toList());

        return AjaxResult.success(voList);
    }
}