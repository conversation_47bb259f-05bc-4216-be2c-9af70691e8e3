package com.hongda.platform.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hongda.common.annotation.Log;
import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.common.enums.BusinessType;
import com.hongda.platform.domain.HongdaPage;
import com.hongda.platform.service.IHongdaPageService;
import com.hongda.common.utils.poi.ExcelUtil;
import com.hongda.common.core.page.TableDataInfo;

/**
 * 页面管理Controller
 * 
 * <AUTHOR>
 * @date 2025-08-15
 */
@RestController
@RequestMapping("/platform/page")
public class HongdaPageController extends BaseController
{
    @Autowired
    private IHongdaPageService hongdaPageService;

    /**
     * 查询页面管理列表
     */
    @PreAuthorize("@ss.hasPermi('platform:page:list')")
    @GetMapping("/list")
    public TableDataInfo list(HongdaPage hongdaPage)
    {
        startPage();
        List<HongdaPage> list = hongdaPageService.selectHongdaPageList(hongdaPage);
        return getDataTable(list);
    }

    /**
     * 导出页面管理列表
     */
    @PreAuthorize("@ss.hasPermi('platform:page:export')")
    @Log(title = "页面管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HongdaPage hongdaPage)
    {
        List<HongdaPage> list = hongdaPageService.selectHongdaPageList(hongdaPage);
        ExcelUtil<HongdaPage> util = new ExcelUtil<HongdaPage>(HongdaPage.class);
        util.exportExcel(response, list, "页面管理数据");
    }

    /**
     * 获取页面管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('platform:page:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(hongdaPageService.selectHongdaPageById(id));
    }

    /**
     * 新增页面管理
     */
    @PreAuthorize("@ss.hasPermi('platform:page:add')")
    @Log(title = "页面管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HongdaPage hongdaPage)
    {
        return toAjax(hongdaPageService.insertHongdaPage(hongdaPage));
    }

    /**
     * 修改页面管理
     */
    @PreAuthorize("@ss.hasPermi('platform:page:edit')")
    @Log(title = "页面管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HongdaPage hongdaPage)
    {
        return toAjax(hongdaPageService.updateHongdaPage(hongdaPage));
    }

    /**
     * 删除页面管理
     */
    @PreAuthorize("@ss.hasPermi('platform:page:remove')")
    @Log(title = "页面管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(hongdaPageService.deleteHongdaPageByIds(ids));
    }
}
