<template>
  <view v-if="!loading && countryList.length > 0" class="highlight-container">
    <view class="section-header">
      <text class="title-main">出海</text>
      <text class="title-gradient">国别</text>
    </view>

    <scroll-view class="country-selector-scroll" scroll-x :show-scrollbar="false">
      <view class="country-selector-inner">
        <view
            v-for="country in countryList"
            :key="country.id"
            class="country-card"
            :class="{ active: selectedCountryId === country.id }"
            @click="selectCountry(country.id)"
        >
          <!-- 1. 直接使用后端返回的完整URL -->
          <image class="country-card-bg" :src="country.listCoverUrl" mode="aspectFill"></image>

          <!-- 2. 【关键修改】同时绑定 :class 和 :style -->
          <view
              class="corner-badge"
              :class="{ 'is-gold': selectedCountryId === country.id }"
              :style="selectedCountryId === country.id ? goldBadgeStyle : blueBadgeStyle"
          >
            <text class="badge-text">{{ country.nameCn }}</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <view class="tabs-wrapper">
      <scroll-view class="tabs" scroll-x="true" :show-scrollbar="false">
        <view
            v-for="tab in tabs"
            :key="tab.id"
            class="tab-item"
            :class="{ active: activeTabId === tab.id }"
            @click="onTabClick(tab.id)"
            :style="activeTabId === tab.id ? activeTabStyle : {}"
        >
          <image class="tab-icon" :src="activeTabId === tab.id ? tab.activeIcon : tab.icon"></image>
          <text class="tab-text">{{ tab.name }}</text>
        </view>
      </scroll-view>
    </view>

    <view v-if="detailLoading" class="content-loading">
      <uni-load-more status="loading" />
    </view>
    <view v-else-if="selectedCountryDetails" class="content-display-area">
      <view class="content-header">
        <text class="content-title">{{ selectedContentTitle }}</text>
        <view class="more-link" @click="navigateToDetailWithTab">
          <text>更多</text>
          <uni-icons type="right" size="14" color="#888"></uni-icons>
        </view>
      </view>
      <view class="summary-content">
        <rich-text :nodes="selectedContent"></rich-text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { getCountryList, getCountryDetail } from '@/api/content/country.js';

// --- 状态定义 ---
const loading = ref(true);
const detailLoading = ref(false);
const countryList = ref([]);
const selectedCountryId = ref(null);
const selectedCountryDetails = ref(null);
const activeTabId = ref('basic');
const assets = ref({}); // 用于存储从缓存读取的静态资源

// --- Tabs 静态配置 ---
const tabsConfig = ref([
  { id: 'basic', name: '基本信息', iconKey: 'icon_tab_basic_normal', activeIconKey: 'icon_tab_basic_active' },
  { id: 'investment', name: '招商政策', iconKey: 'icon_tab_investment_normal', activeIconKey: 'icon_tab_investment_active' },
  { id: 'customs', name: '海关政策', iconKey: 'icon_tab_customs_normal', activeIconKey: 'icon_tab_customs_active' },
  { id: 'tax', name: '税务政策', iconKey: 'icon_tab_tax_normal', activeIconKey: 'icon_tab_tax_active' },
  { id: 'parks', name: '工业园区', iconKey: 'icon_tab_parks_normal', activeIconKey: 'icon_tab_parks_active' },
]);

// --- 计算属性 ---

// 动态计算 tabs 列表，自动从缓存替换图标URL
const tabs = computed(() => {
  return tabsConfig.value.map(tab => ({
    id: tab.id,
    name: tab.name,
    icon: assets.value[tab.iconKey] || '',
    activeIcon: assets.value[tab.activeIconKey] || ''
  }));
});

// 动态计算背景样式
const goldBadgeStyle = computed(() => ({
  backgroundImage: assets.value.bg_badge_gold ? `url('${assets.value.bg_badge_gold}')` : 'none'
}));
const blueBadgeStyle = computed(() => ({
  backgroundImage: assets.value.bg_badge_blue ? `url('${assets.value.bg_badge_blue}')` : 'none'
}));
const activeTabStyle = computed(() => ({
  backgroundImage: assets.value.bg_tab_active_home ? `url('${assets.value.bg_tab_active_home}')` : 'none'
}));

const selectedContentTitle = computed(() => {
  const countryName = selectedCountryDetails.value?.nameCn || '';
  const tabName = tabs.value.find(t => t.id === activeTabId.value)?.name || '';
  return `${countryName} - ${tabName}`;
});

const selectedContent = computed(() => {
  if (!selectedCountryDetails.value) return '<p>暂无相关信息。</p>';
  const contentMapping = {
    basic: selectedCountryDetails.value.introduction,
    investment: selectedCountryDetails.value.investmentPolicy,
    customs: selectedCountryDetails.value.customsPolicy,
    tax: selectedCountryDetails.value.taxPolicy,
    parks: '<p>请点击“更多”查看详细的工业园区列表。</p>'
  };
  return contentMapping[activeTabId.value] || '<p>暂无相关信息。</p>';
});

// --- 方法 ---

const fetchCountryDetails = async (id) => {
  detailLoading.value = true;
  selectedCountryDetails.value = null;
  try {
    const res = await getCountryDetail(id);
    selectedCountryDetails.value = res.data;
  } catch (error) {
    console.error(`获取ID为 ${id} 的国别详情失败:`, error);
  } finally {
    detailLoading.value = false;
  }
};

const fetchFeaturedCountries = async () => {
  loading.value = true;
  try {
    const params = { pageNum: 1, pageSize: 5 };
    const res = await getCountryList(params);
    if (res.data && res.data.length > 0) {
      countryList.value = res.data;
      const firstCountryId = res.data[0].id;
      selectedCountryId.value = firstCountryId;
      await fetchCountryDetails(firstCountryId);
    }
  } catch (error) {
    console.error('获取推荐国别失败:', error);
  } finally {
    loading.value = false;
  }
};

const selectCountry = (id) => {
  if (selectedCountryId.value === id) return;
  selectedCountryId.value = id;
  activeTabId.value = 'basic';
  fetchCountryDetails(id);
};

const onTabClick = (tabId) => {
  activeTabId.value = tabId;
};

const navigateToDetailWithTab = () => {
  if (selectedCountryId.value) {
    uni.navigateTo({
      url: `/pages_sub/pages_country/detail?id=${selectedCountryId.value}&tab=${activeTabId.value}`
    });
  }
};

// --- 生命周期钩子 ---
onMounted(() => {
  assets.value = uni.getStorageSync('staticAssets') || {};
  fetchFeaturedCountries();
});
</script>

<style lang="scss" scoped>
/* 整体容器 */
.highlight-container {
  padding: 32rpx;
  border-radius: 20rpx;
  background: linear-gradient(to bottom, rgba(2, 63, 152, 0.1), rgba(2, 63, 152, 0));
}

/* 标题 */
.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}
.title-main {
  /* "出海"的样式 */
  font-size: 40rpx;
  font-weight: 400;
  color: #023F98;
  font-family: 'YouSheBiaoTiHei', 'Alibaba PuHuiTi 3.0', sans-serif;
}
.title-gradient {
  /* "国别"的样式 */
  font-size: 40rpx;
  font-weight: 400;
  background-image: linear-gradient(91.61deg, #FFAD22 0%, #FFBB87 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-family: 'YouSheBiaoTiHei', 'Alibaba PuHuiTi 3.0', sans-serif;
}

/* 国家选择器 */
.country-selector-scroll {
  width: 100%;
  padding-bottom: 24rpx;
}
.country-selector-inner {
  display: flex;
  flex-direction: row;
}

.country-card {
  position: relative;
  flex-shrink: 0;
  width: 300rpx;
  height: 192rpx;
  border-radius: 16rpx;
  overflow: hidden;
  margin-right: 24rpx;
  transition: transform 0.3s ease, outline 0.3s ease;

  &.active {
    border: 4rpx solid #FFBF51;
  }
}

.country-card-bg {
  width: 100%;
  height: 100%;
}

.corner-badge {
  position: absolute;
  top: 0;
  left: 0;
  width: 150rpx;
  height: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-size: 100% 100%;
  margin-left: -20rpx;
  z-index: 2;

  .badge-text {
    font-size: 24rpx;
    font-weight: 500;
  }
}

/* 3. 【关键修改】使用 class 选择器而不是属性选择器 */
.corner-badge .badge-text {
  color: #FFFFFF; /* 默认为白色 */
}
.corner-badge.is-gold .badge-text {
  color: #23232A; /* 当有 is-gold 类时，文字为深色 */
}


/* Tab 标签栏及以下样式 */
.tabs-wrapper {
  background-color: #F4F4F4;
  border-radius: 16rpx;
}
.tabs {
  display: flex;
  white-space: nowrap;
}
.tab-item {
  display: inline-flex;
  align-items: center;
  gap: 12rpx;
  background-color: #FFFFFF;
  border-radius: 10rpx;
  padding: 12rpx 24rpx;
  margin-right: 20rpx;
  &:last-child { margin-right: 0; }
  .tab-icon { width: 40rpx; height: 40rpx; }
  .tab-text { font-size: 28rpx; color: #9B9A9A; }

  &.active {
    background-size: cover;
    background-position: center;
    .tab-text { color: #23232A; font-weight: bold; }
  }
}

.content-loading {
  padding: 40rpx 0;
}
.content-display-area {
  margin-top: 32rpx;
}
.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}
.content-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.more-link {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #888;
}
.summary-content {
  font-size: 26rpx;
  color: #66666E;
  line-height: 1.7;

  :deep(strong, b) {
    color: #23232A !important;
  }
}
</style>
