<template>
  <div :style="containerStyle">
    <Toolbar
        :editor="editorRef"
        :defaultConfig="toolbarConfig"
        :mode="mode"
        style="border-bottom: 1px solid #ccc"
    />
    <Editor
        :defaultConfig="editorConfig"
        :mode="mode"
        v-model="valueHtml"
        :style="styles"
        @onCreated="handleCreated"
        @onChange="handleChange"
    />
  </div>
</template>

// /src/components/Editor/index.vue

<script setup>
import { ref, computed, watch, shallowRef, onBeforeUnmount } from 'vue';
import '@wangeditor/editor/dist/css/style.css';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import { getToken } from "@/utils/auth";
import '@/assets/styles/editor-style.scss'

const props = defineProps({
  modelValue: { type: String, default: "" },
  width: { type: [Number, String], default: "auto" },
  height: { type: [Number, String], default: "300px" },
  readOnly: { type: Boolean, default: false },
});

const emit = defineEmits(['update:modelValue']);

const editorRef = shallowRef();
const valueHtml = ref(props.modelValue);
const mode = 'default';

// [修改] styles 现在只负责编辑区域的 flex 属性
const styles = computed(() => ({
  flex: 1,
  minHeight: '400px', // 保留一个最小高度
}));

// [修改] containerStyle 现在同时处理宽度和高度，并使用 flex 布局
const containerStyle = computed(() => ({
  width: typeof props.width === 'number' ? `${props.width}px` : props.width,
  height: typeof props.height === 'number' ? `${props.height}px` : props.height,
  border: '1px solid #ccc',
  zIndex: 100,
  display: 'flex',
  flexDirection: 'column'
}));

const toolbarConfig = ref({});

const editorConfig = ref({
  placeholder: '请输入内容...',
  readOnly: props.readOnly,
  MENU_CONF: {
    uploadImage: {
      server: import.meta.env.VITE_APP_BASE_API + "/common/upload",
      fieldName: 'file',
      headers: {
        Authorization: "Bearer " + getToken()
      },
      customInsert(res, insertFn) {
        if (res.code === 200 && res.url) {
          insertFn(res.url, res.originalFilename, res.url);
        } else {
          console.error("图片上传失败:", res.msg);
        }
      },
    },
    uploadVideo: {
      server: import.meta.env.VITE_APP_BASE_API + "/common/upload",
      fieldName: 'file',
      headers: {
        Authorization: "Bearer " + getToken()
      },
      maxFileSize: 50 * 1024 * 1024,
      customInsert(res, insertFn) {
        if (res.code === 200 && res.url) {
          insertFn(res.url);
        } else {
          console.error("视频上传失败:", res.msg);
        }
      },
    }
  }
});

const handleCreated = (editor) => {
  editorRef.value = editor;
};

const handleChange = (editor) => {
  emit('update:modelValue', editor.getHtml());
};

watch(() => props.modelValue, (newValue) => {
  if (newValue !== valueHtml.value) {
    valueHtml.value = newValue;
  }
});

onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor == null) return;
  editor.destroy();
});
</script>