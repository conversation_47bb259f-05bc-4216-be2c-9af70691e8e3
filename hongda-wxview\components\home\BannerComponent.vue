<template>
  <view v-if="swiperList.length > 0" class="banner-container">
    <up-swiper
        :list="swiperList"
        keyName="url"
        :circular="true"
        :autoplay="true"
        :interval="3000"
        :duration="500"
        :indicator="true"
        indicatorActiveColor="#FFFFFF"
        indicatorInactiveColor="rgba(255, 255, 255, 0.5)"

        indicatorMode="dot"

        @click="handleBannerClick"
        imgMode="aspectFill"
        height="296rpx"
        radius="16rpx"
    ></up-swiper>
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
// 步骤 1: 导入正确的广告 API
import { getAdListByPositionApi } from '@/api/platform/ad.js';
import { navigateTo } from '@/utils/navigation.js';
import { getFullImageUrl } from '@/utils/image.js';

const bannerList = ref([]);

// 计算属性，将从接口获取的 imageUrl 转换为完整的图片路径
const swiperList = computed(() =>
    bannerList.value.map(item => ({
      ...item,
      // 步骤 3: 使用广告表正确的图片字段名 imageUrl
      url: getFullImageUrl(item.imageUrl)
    }))
);

// 从接口获取轮播图数据
const fetchBannerData = async () => {
  try {
    // 步骤 2: 调用广告接口，并传入位置代码 'HOME_BANNER'
    const res = await getAdListByPositionApi('HOME_BANNER');
    if (res.data) {
      bannerList.value = res.data;
    }
  } catch (error) {
    console.error('获取轮播图失败:', error);
  }
};

// 点击轮播图项的事件处理
const handleBannerClick = (index) => {
  const bannerItem = bannerList.value[index];
  if (bannerItem) {
    // navigateTo 工具函数会处理跳转逻辑
    navigateTo(bannerItem);
  }
};

// onMounted 生命周期钩子，在组件挂载后执行数据获取
onMounted(() => {
  fetchBannerData();
});
</script>

<style lang="scss" scoped>
.banner-container {
  /* 容器与页面边缘的间距 */
  margin: 24rpx 24rpx;
  border-radius: 16rpx;
  overflow: hidden;

  /* 容器高度，与 up-swiper 的 height 属性保持一致，确保占位正确 */
  height: 296rpx;

  /* 在图片加载出来前的背景色，防止空白 */
  background-color: #f0f2f5;
}
</style>