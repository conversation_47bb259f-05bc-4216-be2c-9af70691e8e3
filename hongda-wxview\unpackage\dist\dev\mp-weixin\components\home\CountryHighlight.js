"use strict";
const common_vendor = require("../../common/vendor.js");
const api_content_country = require("../../api/content/country.js");
if (!Array) {
  const _easycom_uni_load_more2 = common_vendor.resolveComponent("uni-load-more");
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  (_easycom_uni_load_more2 + _easycom_uni_icons2)();
}
const _easycom_uni_load_more = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
if (!Math) {
  (_easycom_uni_load_more + _easycom_uni_icons)();
}
const _sfc_main = {
  __name: "CountryHighlight",
  setup(__props) {
    const loading = common_vendor.ref(true);
    const detailLoading = common_vendor.ref(false);
    const countryList = common_vendor.ref([]);
    const selectedCountryId = common_vendor.ref(null);
    const selectedCountryDetails = common_vendor.ref(null);
    const activeTabId = common_vendor.ref("basic");
    const assets = common_vendor.ref({});
    const tabsConfig = common_vendor.ref([
      { id: "basic", name: "基本信息", iconKey: "icon_tab_basic_normal", activeIconKey: "icon_tab_basic_active" },
      { id: "investment", name: "招商政策", iconKey: "icon_tab_investment_normal", activeIconKey: "icon_tab_investment_active" },
      { id: "customs", name: "海关政策", iconKey: "icon_tab_customs_normal", activeIconKey: "icon_tab_customs_active" },
      { id: "tax", name: "税务政策", iconKey: "icon_tab_tax_normal", activeIconKey: "icon_tab_tax_active" },
      { id: "parks", name: "工业园区", iconKey: "icon_tab_parks_normal", activeIconKey: "icon_tab_parks_active" }
    ]);
    const tabs = common_vendor.computed(() => {
      return tabsConfig.value.map((tab) => ({
        id: tab.id,
        name: tab.name,
        icon: assets.value[tab.iconKey] || "",
        activeIcon: assets.value[tab.activeIconKey] || ""
      }));
    });
    const goldBadgeStyle = common_vendor.computed(() => ({
      backgroundImage: assets.value.bg_badge_gold ? `url('${assets.value.bg_badge_gold}')` : "none"
    }));
    const blueBadgeStyle = common_vendor.computed(() => ({
      backgroundImage: assets.value.bg_badge_blue ? `url('${assets.value.bg_badge_blue}')` : "none"
    }));
    const activeTabStyle = common_vendor.computed(() => ({
      backgroundImage: assets.value.bg_tab_active_home ? `url('${assets.value.bg_tab_active_home}')` : "none"
    }));
    const selectedContentTitle = common_vendor.computed(() => {
      var _a, _b;
      const countryName = ((_a = selectedCountryDetails.value) == null ? void 0 : _a.nameCn) || "";
      const tabName = ((_b = tabs.value.find((t) => t.id === activeTabId.value)) == null ? void 0 : _b.name) || "";
      return `${countryName} - ${tabName}`;
    });
    const selectedContent = common_vendor.computed(() => {
      if (!selectedCountryDetails.value)
        return "<p>暂无相关信息。</p>";
      const contentMapping = {
        basic: selectedCountryDetails.value.introduction,
        investment: selectedCountryDetails.value.investmentPolicy,
        customs: selectedCountryDetails.value.customsPolicy,
        tax: selectedCountryDetails.value.taxPolicy,
        parks: "<p>请点击“更多”查看详细的工业园区列表。</p>"
      };
      return contentMapping[activeTabId.value] || "<p>暂无相关信息。</p>";
    });
    const fetchCountryDetails = async (id) => {
      detailLoading.value = true;
      selectedCountryDetails.value = null;
      try {
        const res = await api_content_country.getCountryDetail(id);
        selectedCountryDetails.value = res.data;
      } catch (error) {
        common_vendor.index.__f__("error", "at components/home/<USER>", `获取ID为 ${id} 的国别详情失败:`, error);
      } finally {
        detailLoading.value = false;
      }
    };
    const fetchFeaturedCountries = async () => {
      loading.value = true;
      try {
        const params = { pageNum: 1, pageSize: 5 };
        const res = await api_content_country.getCountryList(params);
        if (res.data && res.data.length > 0) {
          countryList.value = res.data;
          const firstCountryId = res.data[0].id;
          selectedCountryId.value = firstCountryId;
          await fetchCountryDetails(firstCountryId);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at components/home/<USER>", "获取推荐国别失败:", error);
      } finally {
        loading.value = false;
      }
    };
    const selectCountry = (id) => {
      if (selectedCountryId.value === id)
        return;
      selectedCountryId.value = id;
      activeTabId.value = "basic";
      fetchCountryDetails(id);
    };
    const onTabClick = (tabId) => {
      activeTabId.value = tabId;
    };
    const navigateToDetailWithTab = () => {
      if (selectedCountryId.value) {
        common_vendor.index.navigateTo({
          url: `/pages_sub/pages_country/detail?id=${selectedCountryId.value}&tab=${activeTabId.value}`
        });
      }
    };
    common_vendor.onMounted(() => {
      assets.value = common_vendor.index.getStorageSync("staticAssets") || {};
      fetchFeaturedCountries();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: !loading.value && countryList.value.length > 0
      }, !loading.value && countryList.value.length > 0 ? common_vendor.e({
        b: common_vendor.f(countryList.value, (country, k0, i0) => {
          return {
            a: country.listCoverUrl,
            b: common_vendor.t(country.nameCn),
            c: selectedCountryId.value === country.id ? 1 : "",
            d: common_vendor.s(selectedCountryId.value === country.id ? goldBadgeStyle.value : blueBadgeStyle.value),
            e: country.id,
            f: selectedCountryId.value === country.id ? 1 : "",
            g: common_vendor.o(($event) => selectCountry(country.id), country.id)
          };
        }),
        c: common_vendor.f(tabs.value, (tab, k0, i0) => {
          return {
            a: activeTabId.value === tab.id ? tab.activeIcon : tab.icon,
            b: common_vendor.t(tab.name),
            c: tab.id,
            d: activeTabId.value === tab.id ? 1 : "",
            e: common_vendor.o(($event) => onTabClick(tab.id), tab.id),
            f: common_vendor.s(activeTabId.value === tab.id ? activeTabStyle.value : {})
          };
        }),
        d: detailLoading.value
      }, detailLoading.value ? {
        e: common_vendor.p({
          status: "loading"
        })
      } : selectedCountryDetails.value ? {
        g: common_vendor.t(selectedContentTitle.value),
        h: common_vendor.p({
          type: "right",
          size: "14",
          color: "#909399"
        }),
        i: common_vendor.o(navigateToDetailWithTab),
        j: selectedContent.value
      } : {}, {
        f: selectedCountryDetails.value
      }) : {});
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-edc8cefd"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/home/<USER>
