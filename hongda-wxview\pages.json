{"easycom": {"autoscan": true, "custom": {"uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue", "^up-(.*)": "@/uni_modules/uview-plus/components/u-$1/u-$1.vue", "^mp-html": "@/uni_modules/mp-html/components/mp-html/mp-html.vue"}}, "pages": [{"path": "pages/index/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/article/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/event/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/country/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/profile/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/webview/index", "style": {"navigationStyle": "default", "navigationBarTitleText": "加载中..."}}], "subPackages": [{"root": "pages_sub/pages_article", "pages": [{"path": "detail", "style": {"navigationStyle": "custom"}}]}, {"root": "pages_sub/pages_event", "pages": [{"path": "detail", "style": {"navigationStyle": "custom"}}, {"path": "registration", "style": {"navigationStyle": "custom"}}]}, {"root": "pages_sub/pages_country", "pages": [{"path": "detail", "style": {"navigationStyle": "custom"}}, {"path": "policy_detail", "style": {"navigationStyle": "custom", "enablePullDownRefresh": false, "disableBackButton": true}}]}, {"root": "pages_sub/pages_profile", "pages": [{"path": "orders", "style": {"navigationStyle": "custom"}}, {"path": "contact", "style": {"navigationStyle": "custom"}}]}, {"root": "pages_sub/pages_other", "pages": [{"path": "search", "style": {"navigationStyle": "custom"}}, {"path": "park_detail", "style": {"navigationStyle": "custom"}}, {"path": "login", "style": {"navigationStyle": "custom"}}, {"path": "registration_detail", "style": {"navigationStyle": "custom"}}, {"path": "policy", "style": {"navigationStyle": "custom"}}, {"path": "custom_page", "style": {"navigationStyle": "custom", "navigationBarTitleText": "加载中..."}}]}], "preloadRule": {"pages/index/index": {"network": "all", "packages": ["pages_sub/pages_article"]}, "pages/article/index": {"network": "all", "packages": ["pages_sub/pages_article"]}}, "globalStyle": {"navigationBarTextStyle": "black", "navigationBarBackgroundColor": "#FFFFFF", "backgroundColor": "#FFFFFF"}, "tabBar": {"list": [{"pagePath": "pages/index/index"}, {"pagePath": "pages/article/index"}, {"pagePath": "pages/event/index"}, {"pagePath": "pages/country/index"}, {"pagePath": "pages/profile/index"}]}}