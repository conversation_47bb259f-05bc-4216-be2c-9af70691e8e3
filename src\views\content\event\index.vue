<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="120px">
      <!-- 第一行：活动名称和活动地点 -->
      <el-form-item label="活动名称" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入活动名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="活动地点" prop="location">
        <el-input
          v-model="queryParams.location"
          placeholder="请输入活动地点"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <!-- 第二行：活动状态、报名状态、搜索和重置按钮 -->
      <el-form-item label="活动状态" prop="activityStatus">
        <el-select
          v-model="queryParams.activityStatus"
          placeholder="请选择活动状态"
          clearable
          style="width: 120px"
        >
          <el-option label="未开始" value="0" />
          <el-option label="进行中" value="1" />
          <el-option label="已结束" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="报名状态" prop="registrationStatus">
        <el-select
          v-model="queryParams.registrationStatus"
          placeholder="请选择报名状态"
          clearable
          style="width: 120px"
        >
          <el-option label="未开始" value="0" />
          <el-option label="报名中" value="1" />
          <el-option label="已结束" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮行 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['content:event:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['content:event:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['content:event:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['content:event:export']"
        >导出</el-button>
      </el-col>
      
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table 
      v-loading="loading" 
      :data="eventList" 
      row-key="id" 
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="活动ID" align="center" prop="id" width="80" />
      <el-table-column label="活动名称" align="center" prop="title" show-overflow-tooltip />
      <!-- 报名情况列 -->
      <el-table-column label="报名情况" align="center" width="120">
        <template #default="scope">
          <span v-if="scope.row.maxParticipants > 0">
            {{ scope.row.registeredCount || 0 }} / {{ scope.row.maxParticipants }}
          </span>
          <span v-else>
            {{ scope.row.registeredCount || 0 }} / 不限制
          </span>
        </template>
      </el-table-column>
      <el-table-column label="活动地点" align="center" show-overflow-tooltip>
        <template #default="scope">
          <span>{{ formatEventLocation(scope.row) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="开始时间" align="center" prop="startTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="活动状态" align="center" width="100">
         <template #default="scope">
            <el-tag :type="getActivityStatusType(scope.row.activityStatus)">
              {{ getActivityStatusText(scope.row.activityStatus) }}
            </el-tag>
         </template>
      </el-table-column>
      <el-table-column label="报名状态" align="center" width="100">
         <template #default="scope">
            <el-tag :type="getRegistrationStatusType(scope.row.registrationStatus)">
              {{ getRegistrationStatusText(scope.row.registrationStatus) }}
            </el-tag>
         </template>
      </el-table-column>
      <el-table-column label="是否热门" align="center" prop="isHot" width="100">
        <template #default="scope">
          <el-switch
            v-model="scope.row.isHot"
            :active-value="1"
            :inactive-value="0"
            @change="(value) => handleHotStatusChange(scope.row, value)"
            :loading="scope.row.hotStatusLoading"
          />
        </template>
      </el-table-column>
      <el-table-column label="是否推广" align="center" prop="isPromoted" width="100">
        <template #default="scope">
          <el-switch
            v-model="scope.row.isPromoted"
            :active-value="1"
            :inactive-value="0"
            @change="(value) => handlePromotionStatusChange(scope.row, value)"
            :loading="scope.row.promotionStatusLoading"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-dropdown trigger="click">
            <el-button link type="primary">
              更多操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item 
                  icon="View" 
                  @click="handleViewRegistrations(scope.row)" 
                  v-hasPermi="['content:event:query']">
                  查看报名
                </el-dropdown-item>
                <el-dropdown-item 
                  icon="Edit" 
                  @click="handleUpdate(scope.row)" 
                  v-hasPermi="['content:event:edit']">
                  修改
                </el-dropdown-item>
                <el-dropdown-item 
                  icon="Delete" 
                  @click="handleDelete(scope.row)" 
                  v-hasPermi="['content:event:remove']"
                  :style="{ color: 'var(--el-color-danger)' }">
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改活动管理对话框 -->
    <el-dialog 
      :title="title" 
      v-model="open" 
      width="1000px" 
      append-to-body
    >
      <el-tabs v-model="activeTab">
        <el-tab-pane label="基本信息" name="basicInfo">
          <el-form ref="eventRef" :model="form" :rules="rules" label-width="120px">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="活动名称" prop="title">
                  <el-input 
                    v-model="form.title" 
                    placeholder="请输入活动名称" 
                    maxlength="100"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <!-- <el-form-item label="活动状态" prop="status">
                  <el-radio-group v-model="form.status">
                    <el-radio-button 
                      v-for="dict in manualStatusOptions" 
                      :key="dict.value" 
                      :label="dict.value"
                    >
                      {{dict.label}}
                    </el-radio-button>
                  </el-radio-group>
                </el-form-item> -->
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="活动开始时间" prop="startTime">
                  <el-date-picker 
                    clearable 
                    v-model="form.startTime" 
                    type="datetime" 
                    value-format="YYYY-MM-DD HH:mm:ss" 
                    placeholder="请选择活动开始时间"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="活动结束时间" prop="endTime">
                  <el-date-picker 
                    clearable 
                    v-model="form.endTime" 
                    type="datetime" 
                    value-format="YYYY-MM-DD HH:mm:ss" 
                    placeholder="请选择活动结束时间"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="活动地点" prop="province">
                  <el-cascader
                    v-model="selectedLocation"
                    :options="locationOptions"
                    placeholder="请选择省市区"
                    style="width: 100%"
                    @change="handleLocationChange"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 详细地址独自占据一行 -->
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="详细地址" prop="addressDetail">
                  <el-input
                    v-model="form.addressDetail"
                    placeholder="请输入详细地址（如街道、门牌号等）"
                    maxlength="100"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="最大报名人数" prop="maxParticipants">
                  <el-input-number 
                    v-model="form.maxParticipants" 
                    :min="0" 
                    placeholder="0为不限制"
                    style="width: 100%"
                    controls-position="right"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="报名开始时间" prop="registrationStartTime">
                  <el-date-picker 
                    clearable 
                    v-model="form.registrationStartTime" 
                    type="datetime" 
                    value-format="YYYY-MM-DD HH:mm:ss" 
                    placeholder="请选择报名开始时间"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="报名结束时间" prop="registrationEndTime">
                  <el-date-picker 
                    clearable 
                    v-model="form.registrationEndTime" 
                    type="datetime" 
                    value-format="YYYY-MM-DD HH:mm:ss" 
                    placeholder="请选择报名结束时间"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            

            
            <el-form-item label="活动封面图片" prop="coverImageUrl">
              <image-upload v-model="form.coverImageUrl"/>
            </el-form-item>
            
            <el-form-item label="活动卡片图标" prop="iconUrl">
              <image-upload v-model="form.iconUrl"/>
            </el-form-item>
            
            <el-form-item label="活动简介" prop="summary">
              <el-input 
                v-model="form.summary" 
                placeholder="请输入活动简介，20字以内" 
                maxlength="20"
                show-word-limit
              />
            </el-form-item>
            
            <el-form-item label="卖点" prop="sellPoint">
              <el-input 
                v-model="form.sellPoint" 
                placeholder="请输入活动卖点，15字以内" 
                maxlength="15"
                show-word-limit
              />
            </el-form-item>
            
            <el-form-item label="活动图文详情" prop="details">
              <editor v-model="form.details" :min-height="300"/>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        
        <el-tab-pane label="报名表单设计" name="formDesign">
            <fc-designer ref="designer" :rule="currentFormDefinition" :option="designerOption"></fc-designer>
        </el-tab-pane>
      </el-tabs>
      
      <template #footer>
        <div class="dialog-footer">
          <div class="footer-left">
          </div>
          <div class="footer-right">
            <el-button size="large" @click="cancel">取 消</el-button>
            <el-button type="primary" size="large" @click="handleConfirm">
              确 定
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- 查看报名列表对话框 -->
    <el-dialog :title="registrationDialogTitle" v-model="registrationListOpen" width="1200px" append-to-body>
      <!-- 搜索区域 -->
      <el-form :model="registrationQueryParams" ref="registrationQueryRef" :inline="true" label-width="80px" class="mb15">
        <el-form-item label="用户昵称" prop="nickname">
          <el-input
            v-model="registrationQueryParams.nickname"
            placeholder="请输入用户昵称"
            clearable
            style="width: 200px"
            @keyup.enter="getRegistrationList"
          />
        </el-form-item>
        <el-form-item label="联系方式" prop="phone">
          <el-input
            v-model="registrationQueryParams.phone"
            placeholder="请输入联系方式"
            clearable
            style="width: 200px"
            @keyup.enter="getRegistrationList"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="registrationQueryParams.status" placeholder="请选择状态" clearable style="width: 120px">
            <el-option label="已报名" value="0" />
            <el-option label="已取消" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getRegistrationList">搜索</el-button>
          <el-button icon="Refresh" @click="resetRegistrationQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮行 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleRegistrationOperation('add')" v-hasPermi="['data:registration:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="Edit" :disabled="registrationSingle" @click="handleRegistrationOperation('edit')" v-hasPermi="['data:registration:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="Delete" :disabled="registrationMultiple" @click="handleRegistrationOperation('delete')" v-hasPermi="['data:registration:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="Download" @click="handleExportRegistration" v-hasPermi="['data:registration:export']">导出</el-button>
        </el-col>
      </el-row>

      <el-table 
        v-loading="registrationLoading" 
        :data="currentRegistrationList" 
        border
        @selection-change="handleRegistrationSelectionChange"
        style="width: 100%;"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" type="index" width="60" align="center" />
        <el-table-column label="报名用户" width="200" align="center">
          <template #default="scope">
            <div style="display: flex; align-items: center; justify-content: center;">
              <el-avatar 
                :src="scope.row.userInfo?.avatarUrl" 
                :size="32" 
                style="margin-right: 10px;"
              >
                <el-icon><User /></el-icon>
              </el-avatar>
              <div>
                <div style="font-weight: 500;">{{ scope.row.userInfo?.nickname || '未设置昵称' }}</div>
                <div style="font-size: 12px; color: #999;">ID: {{ scope.row.userId }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="联系方式" width="150" align="center">
          <template #default="scope">
            <span>{{ scope.row.userInfo?.phone || '未绑定' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="报名时间" align="center" prop="registrationTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.registrationTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.status === 0 ? 'success' : 'info'" size="small">
              {{ scope.row.status === 0 ? '已报名' : '已取消' }}
            </el-tag>
          </template>
        </el-table-column>
          
          <!-- 动态报名表单字段列 -->
          <el-table-column 
            v-for="header in dynamicTableHeaders" 
            :key="header.prop" 
            :prop="header.prop" 
            :label="header.label" 
            :width="getColumnWidth(header.prop)"
            align="center"
            show-overflow-tooltip
          >
            <template #default="scope">
              <span>{{ getFormFieldValue(scope.row, header.prop) }}</span>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="150" align="center" fixed="right">
            <template #default="scope">
              <el-button link type="success" size="small" @click="handleRegistrationOperation('edit', scope.row)" v-hasPermi="['data:registration:edit']">修改</el-button>
              <el-button link type="danger" size="small" @click="handleRegistrationOperation('delete', scope.row)" v-hasPermi="['data:registration:remove']">删除</el-button>
            </template>
          </el-table-column>
      </el-table>
      
      <pagination
        v-show="registrationTotal > 0"
        :total="registrationTotal"
        v-model:page="registrationQueryParams.pageNum"
        v-model:limit="registrationQueryParams.pageSize"
        @pagination="getRegistrationList"
      /> 
    </el-dialog>

    <!-- 推广配置对话框 -->
    <el-dialog :title="promotionDialogTitle" v-model="promotionDialogOpen" width="600px" append-to-body>
      <el-form ref="promotionFormRef" :model="promotionForm" :rules="promotionRules" label-width="120px">
        <el-form-item label="推广标题" prop="promotionTitle">
          <el-input v-model="promotionForm.promotionTitle" placeholder="请输入推广标题" maxlength="100" show-word-limit />
        </el-form-item>
        
        <el-form-item label="推广图片" prop="promotionImageUrl">
          <image-upload v-model="promotionForm.promotionImageUrl" />
        </el-form-item>
        
        <el-form-item label="推广开始时间" prop="promotionStartTime">
          <el-date-picker 
            v-model="promotionForm.promotionStartTime" 
            type="datetime" 
            value-format="YYYY-MM-DD HH:mm:ss" 
            placeholder="请选择推广开始时间"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="推广结束时间" prop="promotionEndTime">
          <el-date-picker 
            v-model="promotionForm.promotionEndTime" 
            type="datetime" 
            value-format="YYYY-MM-DD HH:mm:ss" 
            placeholder="请选择推广结束时间"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="推广排序" prop="promotionSortOrder">
          <el-input-number v-model="promotionForm.promotionSortOrder" :min="0" controls-position="right" style="width: 100%" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitPromotionForm">确 定</el-button>
          <el-button @click="cancelPromotionForm">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新增/修改报名对话框 -->
    <el-dialog :title="registrationFormTitle" v-model="registrationFormOpen" width="700px" append-to-body>
      <el-form ref="registrationFormRef" :model="registrationFormData" label-width="100px">
        <el-form-item label="报名用户ID" prop="userId">
          <el-input v-model="registrationFormData.userId" placeholder="请输入用户ID" />
        </el-form-item>
        <el-form-item label="报名状态" prop="status">
          <el-radio-group v-model="registrationFormData.status">
            <el-radio :label="0">已报名</el-radio>
            <el-radio :label="1">已取消</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- 动态表单渲染区域 -->
        <div v-if="currentFormDefinition && currentFormDefinition.length > 0">
          <el-divider content-position="left">报名表单内容</el-divider>
          <div style="background: #f9f9f9; padding: 15px; border-radius: 4px;">
            <form-create 
              v-model="dynamicFormValue" 
              :rule="currentFormDefinition" 
              :option="formCreateOption"
              :key="formCreateKey"
              ref="dynamicFormRef"
            />
          </div>
        </div>
        <el-alert v-else title="该活动暂无自定义表单设计" type="info" show-icon :closable="false" />
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitRegistrationForm">确 定</el-button>
          <el-button @click="cancelRegistrationForm">取 消</el-button>
        </div>
      </template>
    </el-dialog>


  </div>
</template>

<script setup name="Event">
import { ref, reactive, toRefs, getCurrentInstance, nextTick, computed } from 'vue';
const { proxy } = getCurrentInstance();

// 图标导入
import { 
  InfoFilled, 
  Clock, 
  UserFilled, 
  Document, 
  Check, 
  DocumentAdd,
  User,
  Location
} from '@element-plus/icons-vue';

// API导入
import { listEvent, getEvent, delEvent, addEvent, updateEvent, changeEventHotStatus, changeEventPromotionStatus } from "@/api/content/event";
import { getFormDefinition, saveFormDefinition } from "@/api/content/form";
import { listRegistration, addRegistration, updateRegistration, delRegistration } from "@/api/data/registration";

// 导入全国省市区数据
import { regionData } from 'element-china-area-data';

// 字典
const { hongda_event_status } = proxy.useDict('hongda_event_status');

/**
 * 格式化活动地点 - 只显示省市信息
 * @param {Object} event 活动对象
 * @returns {String} 格式化后的地点信息
 */
const formatEventLocation = (event) => {
  // 优先使用省市字段
  if (event.province && event.city) {
    return event.province === event.city ? event.city : `${event.province}${event.city}`;
  }
  
  // 如果有city字段，直接使用
  if (event.city && event.city.trim()) {
    return event.city.trim();
  }
  
  // 如果只有完整地址，尝试提取省市信息
  if (event.location && event.location.trim()) {
    const location = event.location.trim();
    // 简单的省市提取逻辑，匹配常见的省市格式
    const match = location.match(/^(.+?省|.+?市|.+?自治区|.+?特别行政区)(.+?市|.+?区|.+?县)?/);
    if (match) {
      const province = match[1];
      const city = match[2];
      if (city) {
        return province === city ? city : `${province}${city}`;
      }
      return province;
    }
  }
  
  return '待定';
};

// 基础状态
const eventList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const activeTab = ref("basicInfo");


// 表单相关
const designer = ref(null);
const currentFormDefinition = ref(null);

// 报名管理相关状态
const registrationState = reactive({
  listOpen: false,
  loading: false,
  dialogTitle: "",
  currentList: [],
  total: 0,
  formOpen: false,
  formTitle: "",
  formData: {},
  single: true,
  multiple: true,
  ids: []
});

// 动态表单相关
const dynamicFormValue = ref({});
const formCreateKey = ref(0);
const dynamicFormRef = ref(null);

// 省市区级联选择器相关
const selectedLocation = ref([]);

// 推广相关数据
const promotionDialogOpen = ref(false);
const promotionDialogTitle = ref("");
const currentPromotionEvent = ref(null);

const promotionForm = reactive({
  id: null,
  isPromoted: 0,
  promotionTitle: '',
  promotionImageUrl: '',
  promotionStartTime: null,
  promotionEndTime: null,
  promotionSortOrder: 0
});

const promotionRules = {
  promotionTitle: [
    { required: true, message: "推广标题不能为空", trigger: "blur" }
  ],
  promotionImageUrl: [
    { required: true, message: "推广图片不能为空", trigger: "blur" }
  ]
};

// 省市区级联数据（使用全国数据）
const locationOptions = ref(regionData);

// 查询参数
const registrationQueryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  eventId: null,
  nickname: null,
  phone: null,
  status: null
});

// 配置项
const formCreateOption = ref({
  resetBtn: false,
  submitBtn: false,
  globalClass: 'form-create-custom',
  form: {
    labelWidth: '120px',
    labelPosition: 'right'
  }
});

const designerOption = ref({
  height: '500px'
});

// 主数据和表单
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    title: null,
    location: null,
    activityStatus: null,
    registrationStatus: null,
  },
  rules: {
    title: [{ required: true, message: "活动名称不能为空", trigger: "blur" }],
    startTime: [{ required: true, message: "活动开始时间不能为空", trigger: "change" }],
    province: [{ required: true, message: "请选择活动地点", trigger: "change" }],
    addressDetail: [{ required: true, message: "详细地址不能为空", trigger: "blur" }],
  }
});

const { queryParams, form, rules } = toRefs(data);

// 计算属性 - 简化解构赋值
const {
  listOpen: registrationListOpen,
  loading: registrationLoading,
  dialogTitle: registrationDialogTitle,
  currentList: currentRegistrationList,
  total: registrationTotal,
  formOpen: registrationFormOpen,
  formTitle: registrationFormTitle,
  formData: registrationFormData,
  single: registrationSingle,
  multiple: registrationMultiple,
  ids: registrationIds
} = toRefs(registrationState);

const dynamicTableHeaders = computed(() => {
  if (!currentFormDefinition.value || !Array.isArray(currentFormDefinition.value)) {
    return [];
  }
  return currentFormDefinition.value.map(rule => ({
    prop: rule.field,
    label: rule.title
  }));
});

// 智能状态管理：过滤出运营人员需要手动设置的状态
const manualStatusOptions = computed(() => {
  if (!hongda_event_status.value || !Array.isArray(hongda_event_status.value)) {
    return [];
  }
  
  // 手动设置的状态：只保留报名中(1)、已结束(2)
  const manualStatusValues = ['1', '2'];
  
  return hongda_event_status.value.filter(dict => 
    manualStatusValues.includes(dict.value)
  );
});

// 当前选择状态的说明文本
const currentStatusDescription = computed(() => {
  const statusDescriptions = {
    '1': '活动开放报名，用户可以正常报名参与',
    '2': '活动已结束，用户无法报名'
  };
  
  return statusDescriptions[form.value?.status] || '请选择活动状态';
});

// 通用API调用函数
const apiCall = async (apiFunc, params = null) => {
  try {
    return await apiFunc(params);
  } catch (error) {
    proxy.$modal.msgError(error.message || "操作失败");
    throw error;
  }
};

// 重置表单数据
const resetFormData = (type = 'event') => {
  if (type === 'event') {
    form.value = {
      id: null,
      title: null,
      iconUrl: null,
      coverImageUrl: null,
      details: '',
      summary: null,
      sellPoint: null,
      startTime: null,
      endTime: null,
      location: null,
      province: null,
      city: null,
      district: null,
      addressDetail: null,
      registrationDeadline: null,
      registrationStartTime: null,
      registrationEndTime: null,
      maxParticipants: 0,
      status: "0",
      isHot: 0
    };
    // 重置级联选择器
    selectedLocation.value = [];
    proxy.resetForm("eventRef");
    activeTab.value = "basicInfo";
    currentFormDefinition.value = null;
    if(designer.value) {
      designer.value.setRule([]);
    }
  } else if (type === 'registration') {
    registrationFormData.value = {
      id: null,
      eventId: registrationQueryParams.eventId,
      userId: null,
      status: 0
    };
    dynamicFormValue.value = {};
    formCreateKey.value += 1;
  }
};

// 处理用户信息提取
const extractUserInfo = (formData) => {
  try {
    const data = typeof formData === 'string' ? JSON.parse(formData) : formData;
    
    // 获取所有的键值对
    const entries = Object.entries(data || {});
    
    let nickname = '未填写姓名';
    let phone = '未填写';
    
    // 遍历所有字段，通过字段名或值的特征来识别姓名和电话
    entries.forEach(([key, value]) => {
      if (value && typeof value === 'string' && value.trim()) {
        const keyLower = key.toLowerCase();
        const valueTrimmed = value.trim();
        
        // 识别姓名字段（通过字段名关键词）
        if (keyLower.includes('name') || keyLower.includes('姓名') || 
            keyLower.includes('用户') || keyLower.includes('nickname') || 
            keyLower.includes('昵称') || keyLower.includes('真实姓名') ||
            keyLower.includes('realname') || keyLower.includes('username')) {
          nickname = valueTrimmed;
        }
        
        // 识别电话字段（通过字段名关键词或值的格式）
        if (keyLower.includes('phone') || keyLower.includes('电话') || 
            keyLower.includes('手机') || keyLower.includes('联系') ||
            keyLower.includes('tel') || keyLower.includes('mobile') ||
            // 通过值的格式识别电话号码（11位数字或带区号的格式）
            /^1[3-9]\d{9}$/.test(valueTrimmed) || 
            /^0\d{2,3}-?\d{7,8}$/.test(valueTrimmed) ||
            /^\d{3}-\d{8}$/.test(valueTrimmed)) {
          phone = valueTrimmed;
        }
      }
    });
    
    // 如果通过关键词没找到姓名，尝试通过值的特征来猜测
    if (nickname === '未填写姓名') {
      entries.forEach(([key, value]) => {
        if (value && typeof value === 'string' && value.trim()) {
          const valueTrimmed = value.trim();
          // 如果值看起来像中文姓名（2-4个中文字符）且不是电话号码
          if (/^[\u4e00-\u9fa5]{2,4}$/.test(valueTrimmed) && 
              !/^\d+$/.test(valueTrimmed)) {
            nickname = valueTrimmed;
          }
          // 如果值看起来像英文姓名（包含空格的字母组合）
          else if (/^[a-zA-Z\s]{2,20}$/.test(valueTrimmed) && 
                   valueTrimmed.includes(' ')) {
            nickname = valueTrimmed;
          }
        }
      });
    }
    
    return {
      nickname,
      phone,
      avatarUrl: null
    };
  } catch (error) {
    console.error('解析用户信息失败:', error);
    return {
      nickname: '数据解析失败',
      phone: '未填写',
      avatarUrl: null
    };
  }
};

// 事件处理方法
function getList() {
  loading.value = true;
  apiCall(listEvent, queryParams.value).then(response => {
    let filteredRows = response.rows || [];
    
    // 前端筛选：按活动状态过滤
    if (queryParams.value.activityStatus !== null && queryParams.value.activityStatus !== '') {
      const targetActivityStatus = parseInt(queryParams.value.activityStatus);
      filteredRows = filteredRows.filter(event => {
        return event.activityStatus === targetActivityStatus;
      });
    }
    
    // 前端筛选：按报名状态过滤
    if (queryParams.value.registrationStatus !== null && queryParams.value.registrationStatus !== '') {
      const targetRegistrationStatus = parseInt(queryParams.value.registrationStatus);
      filteredRows = filteredRows.filter(event => {
        return event.registrationStatus === targetRegistrationStatus;
      });
    }
    
    eventList.value = filteredRows;
    total.value = filteredRows.length; // 更新总数为筛选后的数量
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  });
}

function cancel() {
  open.value = false;
  resetFormData('event');
}

// 处理级联选择器变化
function handleLocationChange(value) {
  if (value && value.length === 3) {
    form.value.province = value[0];
    form.value.city = value[1];
    form.value.district = value[2];
  } else {
    form.value.province = null;
    form.value.city = null;
    form.value.district = null;
  }
}

// 根据省市区字段设置级联选择器的值
function setLocationFromForm() {
  if (form.value.province && form.value.city && form.value.district) {
    selectedLocation.value = [form.value.province, form.value.city, form.value.district];
  } else {
    selectedLocation.value = [];
  }
}

function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

function handleAdd() {
  resetFormData('event');
  open.value = true;
  title.value = "添加活动";
}

async function handleUpdate(row) {
  resetFormData('event');
  const _id = row?.id || ids.value[0];

  const response = await apiCall(getEvent, _id);
  form.value = response.data;
  
  // 设置级联选择器的值
  setLocationFromForm();
  
  open.value = true;
  title.value = "修改活动";

  nextTick(async () => {
    try {
      const formDefRes = await getFormDefinition(_id);
      if (formDefRes.data && formDefRes.data.fieldsJson) {
        designer.value.setRule(JSON.parse(formDefRes.data.fieldsJson));
      } else {
        designer.value.setRule([]);
      }
    } catch (error) {
       designer.value.setRule([]);
    }
  });
}

function submitForm() {
  proxy.$refs["eventRef"].validate(valid => {
    if (valid) {
      const isUpdate = form.value.id != null;
      const apiFunc = isUpdate ? updateEvent : addEvent;
      
      apiCall(apiFunc, form.value).then(response => {
        if (!isUpdate) {
          form.value.id = response.data.id; 
          title.value = "修改活动"; 
          activeTab.value = "formDesign";
          proxy.$modal.msgSuccess("新增活动成功，请继续设计报名表单！");
        } else {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
        }
        getList();
      });
    }
  });
}

function handleDelete(row) {
  const _ids = row?.id || ids.value;
  proxy.$modal.confirm('是否确认删除活动管理编号为"' + _ids + '"的数据项？').then(() => {
    return apiCall(delEvent, _ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

function handleExport() {
  proxy.download('content/event/export', {
    ...queryParams.value
  }, `event_${new Date().getTime()}.xlsx`);
}



function handleConfirm() {
  if (activeTab.value === 'formDesign') {
    // 在报名表单设计标签页时，保存表单设计并关闭对话框
    if (!form.value.id) {
      proxy.$modal.msgError("活动ID不存在，无法保存！");
      return;
    }
    const formJson = designer.value.getRule();
    const dataToSave = {
       eventId: form.value.id,
       fieldsJson: JSON.stringify(formJson)
    };
    apiCall(saveFormDefinition, dataToSave).then(() => {
       proxy.$modal.msgSuccess("报名表单设计已保存！");
       open.value = false;
    });
  } else {
    // 在基本信息标签页时，调用原有的 submitForm 逻辑
    submitForm();
  }
}

async function handleViewRegistrations(row) {
  registrationQueryParams.eventId = row.id;
  registrationQueryParams.pageNum = 1;
  registrationDialogTitle.value = `【${row.title}】的报名列表`;
  
  try {
    const formDefRes = await getFormDefinition(row.id);
    currentFormDefinition.value = formDefRes.data?.fieldsJson ? 
      JSON.parse(formDefRes.data.fieldsJson) : [];
  } catch (error) {
    currentFormDefinition.value = [];
  }
  
  registrationListOpen.value = true;
  getRegistrationList();
}

function getRegistrationList() {
  registrationLoading.value = true;
  apiCall(listRegistration, registrationQueryParams).then(response => {
    // 处理用户信息
    const processedRows = response.rows.map(row => ({
      ...row,
      userInfo: row.formData ? extractUserInfo(row.formData) : {
        nickname: '未知用户',
        phone: '未绑定',
        avatarUrl: null
      }
    }));
    
    // 前端过滤
    let filteredRows = processedRows;
    if (registrationQueryParams.nickname?.trim()) {
      filteredRows = filteredRows.filter(row => 
        row.userInfo.nickname?.toLowerCase().includes(registrationQueryParams.nickname.toLowerCase())
      );
    }
    if (registrationQueryParams.phone?.trim()) {
      filteredRows = filteredRows.filter(row => 
        row.userInfo.phone?.includes(registrationQueryParams.phone)
      );
    }
    
    currentRegistrationList.value = filteredRows;
    registrationTotal.value = response.total || 0;
    registrationLoading.value = false;
  }).catch(() => {
    registrationLoading.value = false;
  });
}



// 统一的报名操作处理
function handleRegistrationOperation(operation, row = null) {
  const operationMap = {
    add: () => setupRegistrationForm(),
    edit: () => {
      const targetRow = row || currentRegistrationList.value.find(item => 
        item.id === registrationIds.value[0]
      );
      if (!targetRow) {
        proxy.$modal.msgError("请选择一行数据进行修改");
        return;
      }
      setupRegistrationForm(targetRow);
    },
    delete: () => {
      const _ids = row ? [row.id] : registrationIds.value;
      const _names = row ? (row.userInfo?.nickname || '未知用户') : `${registrationIds.value.length}条记录`;
      
      if (_ids.length === 0) {
        proxy.$modal.msgError("请选择要删除的数据");
        return;
      }
      
      proxy.$modal.confirm(`是否确认删除${_names}的报名数据？`).then(() => {
        const deletePromises = _ids.map(id => apiCall(delRegistration, id));
        return Promise.all(deletePromises);
      }).then(() => {
        getRegistrationList();
        proxy.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }
  };
  
  operationMap[operation]?.();
}

const setupRegistrationForm = (row = null) => {
  if (row) {
    // 修改模式
    let parsedFormData = {};
    try {
      if (row.formData) {
        parsedFormData = typeof row.formData === 'string' ? JSON.parse(row.formData) : row.formData;
      }
    } catch (error) {
      parsedFormData = {};
    }
    
    registrationFormData.value = {
      id: row.id,
      eventId: row.eventId,
      userId: row.userId,
      status: row.status
    };
    
    registrationFormTitle.value = "修改报名";
    
    // 设置表单数据
    nextTick(() => {
      setTimeout(() => {
        const emptyFormData = {};
        if (currentFormDefinition.value && Array.isArray(currentFormDefinition.value)) {
          currentFormDefinition.value.forEach(field => {
            if (field.field) {
              emptyFormData[field.field] = getDefaultValue(field.type);
            }
          });
        }
        
        const finalFormData = { ...emptyFormData, ...parsedFormData };
        dynamicFormValue.value = finalFormData;
        
        // 使用API设置值
        if (dynamicFormRef.value?.api?.setValue) {
          try {
            Object.entries(finalFormData).forEach(([key, value]) => {
              dynamicFormRef.value.api.setValue(key, value);
            });
          } catch (error) {
            console.warn('设置表单值失败:', error);
          }
        }
      }, 300);
    });
  } else {
    // 新增模式
    resetFormData('registration');
    registrationFormTitle.value = "新增";
    
    nextTick(() => {
      if (dynamicFormRef.value?.api?.clearValue) {
        try {
          dynamicFormRef.value.api.clearValue();
        } catch (error) {
          console.warn('清除表单值失败:', error);
        }
      }
      setTimeout(() => {
        dynamicFormValue.value = {};
      }, 100);
    });
  }
  
  registrationFormOpen.value = true;
  formCreateKey.value += 1;
};

// 获取字段默认值
const getDefaultValue = (fieldType) => {
  const defaultValues = {
    checkbox: [],
    switch: false,
    inputNumber: null
  };
  return defaultValues[fieldType] || '';
};

/**
 * 获取表单字段值
 * @param {Object} row 报名记录行数据
 * @param {String} fieldName 字段名
 * @returns {String} 格式化后的字段值
 */
const getFormFieldValue = (row, fieldName) => {
  try {
    const formData = typeof row.formData === 'string' ? JSON.parse(row.formData) : row.formData;
    const value = formData?.[fieldName];
    
    if (value === null || value === undefined || value === '') {
      return '未填写';
    }
    
    // 处理数组类型（如多选框）
    if (Array.isArray(value)) {
      return value.length > 0 ? value.join(', ') : '未选择';
    }
    
    // 处理布尔类型（如开关）
    if (typeof value === 'boolean') {
      return value ? '是' : '否';
    }
    
    // 处理对象类型
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    
    return String(value);
  } catch (error) {
    console.error('解析表单数据失败:', error);
    return '数据错误';
  }
};

/**
 * 根据字段类型动态设置列宽
 * @param {String} fieldName 字段名
 * @returns {String} 列宽度
 */
const getColumnWidth = (fieldName) => {
  // 根据字段名或类型设置不同的宽度
  const fieldLower = fieldName.toLowerCase();
  
  // 电话、手机号等较长的字段
  if (fieldLower.includes('phone') || fieldLower.includes('电话') || 
      fieldLower.includes('手机') || fieldLower.includes('联系')) {
    return '150';
  }
  
  // 姓名、昵称等较短的字段
  if (fieldLower.includes('name') || fieldLower.includes('姓名') || 
      fieldLower.includes('昵称') || fieldLower.includes('nickname')) {
    return '120';
  }
  
  // 地址、详细信息等较长的字段
  if (fieldLower.includes('address') || fieldLower.includes('地址') || 
      fieldLower.includes('详细') || fieldLower.includes('备注') || 
      fieldLower.includes('remark') || fieldLower.includes('description')) {
    return '200';
  }
  
  // 默认宽度
  return '150';
};

function cancelRegistrationForm() {
  registrationFormOpen.value = false;
  resetFormData('registration');
}

function submitRegistrationForm() {
  proxy.$refs["registrationFormRef"].validate(valid => {
    if (valid) {
      // 验证动态表单
      if (dynamicFormRef.value && currentFormDefinition.value && currentFormDefinition.value.length > 0) {
        try {
          if (typeof dynamicFormRef.value.validate === 'function') {
            const validateResult = dynamicFormRef.value.validate();
            if (validateResult && typeof validateResult.then === 'function') {
              validateResult.then(doSubmitRegistration).catch(() => {
                proxy.$modal.msgError("请完善报名表单信息");
              });
            } else {
              if (validateResult) {
                doSubmitRegistration();
              } else {
                proxy.$modal.msgError("请完善报名表单信息");
              }
            }
          } else {
            doSubmitRegistration();
          }
        } catch (error) {
          doSubmitRegistration();
        }
      } else {
        doSubmitRegistration();
      }
    }
  });
}

function doSubmitRegistration() {
  const formData = {
    ...registrationFormData.value,
    formData: JSON.stringify(dynamicFormValue.value || {})
  };
  
  const isUpdate = formData.id;
  const apiFunc = isUpdate ? updateRegistration : addRegistration;
  
  apiCall(apiFunc, formData).then(() => {
    proxy.$modal.msgSuccess(isUpdate ? "修改成功" : "新增成功");
    registrationFormOpen.value = false;
    getRegistrationList();
  });
}

function handleExportRegistration() {
  if (!registrationQueryParams.eventId) {
    proxy.$modal.msgError("活动ID不存在，无法导出");
    return;
  }
  
  proxy.download('data/registration/export-by-event', {
    eventId: registrationQueryParams.eventId,
    status: registrationQueryParams.status,
    nickname: registrationQueryParams.nickname,
    phone: registrationQueryParams.phone
  }, `registration_${new Date().getTime()}.xlsx`);
}

function resetRegistrationQuery() {
  Object.assign(registrationQueryParams, {
    nickname: null,
    phone: null,
    status: null,
    pageNum: 1
  });
  getRegistrationList();
}

function handleRegistrationSelectionChange(selection) {
  registrationIds.value = selection.map(item => item.id);
  registrationSingle.value = selection.length !== 1;
  registrationMultiple.value = !selection.length;
}

async function handleHotStatusChange(row, newValue) {
  const statusText = newValue === 1 ? '热门' : '普通';
  
  try {
    await proxy.$modal.confirm(`确认将活动"${row.title}"设置为${statusText}活动？`, "状态变更", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    });
        
    row.hotStatusLoading = true;
    
    await apiCall(changeEventHotStatus, {
      id: row.id,
      isHot: newValue
    });
    
    proxy.$modal.msgSuccess(`活动状态已更新为${statusText}活动`);
    
  } catch (error) {
    if (error === 'cancel') {
      row.isHot = row.isHot === 1 ? 0 : 1;
    } else {
      row.isHot = row.isHot === 1 ? 0 : 1;
      proxy.$modal.msgError("状态更新失败，请稍后重试");
    }
  } finally {
    row.hotStatusLoading = false;
  }
}

/**
 * 处理推广状态变化
 */
function handlePromotionStatusChange(row, value) {
  if (value === 1) {
    // 开启推广，显示配置对话框
    currentPromotionEvent.value = row;
    promotionDialogTitle.value = "配置活动推广";
    
    // 使用活动信息作为默认值
    promotionForm.id = row.id;
    promotionForm.isPromoted = 1;
    promotionForm.promotionTitle = row.title;
    promotionForm.promotionImageUrl = row.coverImageUrl;
    promotionForm.promotionStartTime = row.startTime;
    promotionForm.promotionEndTime = row.endTime;
    promotionForm.promotionSortOrder = 0;
    
    promotionDialogOpen.value = true;
  } else {
    // 关闭推广，直接提交
    proxy.$modal.confirm('确认关闭该活动的推广功能？').then(() => {
      submitPromotionStatusChange(row.id, { id: row.id, isPromoted: 0 });
    }).catch(() => {
      // 取消操作，恢复开关状态
      row.isPromoted = 1;
    });
  }
}

/**
 * 提交推广配置表单
 */
function submitPromotionForm() {
  proxy.$refs["promotionFormRef"].validate(valid => {
    if (valid) {
      submitPromotionStatusChange(promotionForm.id, promotionForm);
    }
  });
}

/**
 * 提交推广状态变更
 */
function submitPromotionStatusChange(eventId, formData) {
  const targetRow = eventList.value.find(item => item.id === eventId);
  if (targetRow) {
    targetRow.promotionStatusLoading = true;
  }
  
  changeEventPromotionStatus(formData).then(response => {
    proxy.$modal.msgSuccess(formData.isPromoted === 1 ? "推广开启成功" : "推广关闭成功");
    promotionDialogOpen.value = false;
    getList(); // 刷新列表
  }).catch(error => {
    proxy.$modal.msgError("操作失败：" + error.message);
    // 恢复开关状态
    if (targetRow) {
      targetRow.isPromoted = targetRow.isPromoted === 1 ? 0 : 1;
    }
  }).finally(() => {
    if (targetRow) {
      targetRow.promotionStatusLoading = false;
    }
  });
}

/**
 * 取消推广配置
 */
function cancelPromotionForm() {
  promotionDialogOpen.value = false;
  // 恢复开关状态
  if (currentPromotionEvent.value) {
    currentPromotionEvent.value.isPromoted = 0;
  }
}

/**
 * 获取活动状态标签类型
 */
const getActivityStatusType = (status) => {
  switch (status) {
    case 0: return 'info';     // 未开始 - 灰色
    case 1: return 'success';  // 进行中 - 绿色
    case 2: return 'danger';   // 已结束 - 红色
    default: return 'info';
  }
};

/**
 * 获取活动状态文本
 */
const getActivityStatusText = (status) => {
  switch (status) {
    case 0: return '未开始';
    case 1: return '进行中';
    case 2: return '已结束';
    default: return '未知';
  }
};

/**
 * 获取报名状态标签类型
 */
const getRegistrationStatusType = (status) => {
  switch (status) {
    case 0: return 'warning';  // 未开始 - 橙色
    case 1: return 'success';  // 报名中 - 绿色
    case 2: return 'info';     // 已结束 - 灰色
    default: return 'info';
  }
};

/**
 * 获取报名状态文本
 */
const getRegistrationStatusText = (status) => {
  switch (status) {
    case 0: return '未开始';
    case 1: return '报名中';
    case 2: return '已结束';
    default: return '未知';
  }
};



// 初始化
getList();
</script>

<style scoped>
.form-create-custom {
  margin-top: 10px;
}

.el-dialog__body {
  max-height: 70vh;
  overflow-y: auto;
}

/* 对话框底部样式 */
.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.footer-left {
  flex: 1;
}

.footer-right {
  display: flex;
  gap: 12px;
}


</style>
