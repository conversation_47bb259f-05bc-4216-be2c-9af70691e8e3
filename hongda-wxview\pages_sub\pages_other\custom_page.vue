<template>
  <view v-if="pageData && !loading" class="page-container">
    <view class="fixed-header" :style="{ height: headerHeight + 'px' }">
      <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
      <view class="custom-nav-bar" :style="{ height: navBarHeight + 'px' }">
        <view class="nav-back-button" @click="goBack">
          <u-icon name="arrow-left" color="#000000" size="22"></u-icon>
        </view>
        <view class="nav-title">{{ pageData.title || '详情' }}</view>
      </view>
    </view>

    <scroll-view scroll-y class="scrollable-content" :style="{ paddingTop: headerHeight + 'px' }">

      <view class="main-content">
        <view class="article-title">{{ pageData.title }}</view>

        <view class="article-meta-new">
          <view class="meta-left">
            <text class="meta-text">发布于 {{ formatDate(pageData.createTime, 'YYYY-MM-DD') }}</text>
          </view>
        </view>

        <view class="content-card">
          <view class="content-body">
            <mp-html :content="pageData.content" :tag-style="tagStyle" lazy-load/>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <u-loading-page v-else-if="loading" loading-text="正在加载..." :loading="loading"></u-loading-page>

  <view v-else class="error-state">
    <view class="empty-icon">⚠️</view>
    <text class="empty-title">页面加载失败</text>
    <text class="empty-desc">无法获取页面内容，请稍后重试</text>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { getPageDetailsApi } from '@/pages_sub/pages_other/api/platform/page.js';
import { formatDate } from '@/utils/date.js';
// [新增] 导入资讯详情页的富文本样式配置
import { tagStyle } from '@/utils/mpHtmlStyles.js';

const loading = ref(true);
const pageData = ref(null);

// --- [新增] 自定义导航栏所需的状态和方法 ---
const statusBarHeight = ref(0);
const navBarHeight = ref(0);
const headerHeight = ref(0);

const getNavBarInfo = () => {
  try {
    const systemInfo = uni.getSystemInfoSync();
    statusBarHeight.value = systemInfo.statusBarHeight || 20;
    // 胶囊按钮在小程序中可用，H5等环境会报错，做兼容处理
    // #ifdef MP-WEIXIN
    const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
    navBarHeight.value = menuButtonInfo.height + (menuButtonInfo.top - statusBarHeight.value) * 2;
    headerHeight.value = menuButtonInfo.bottom + (menuButtonInfo.top - statusBarHeight.value);
    // #endif
    // #ifndef MP-WEIXIN
    navBarHeight.value = 44; // H5等其他环境给一个默认高度
    headerHeight.value = statusBarHeight.value + navBarHeight.value;
    // #endif
  } catch (e) {
    statusBarHeight.value = 20;
    navBarHeight.value = 44;
    headerHeight.value = 64;
  }
};

const goBack = () => {
  uni.navigateBack({ delta: 1 });
};
// --- 导航栏逻辑结束 ---

onLoad(async (options) => {
  getNavBarInfo(); // 初始化导航栏高度

  const pageId = options.id;
  if (!pageId) {
    loading.value = false;
    console.error("页面ID缺失");
    return;
  }

  try {
    const res = await getPageDetailsApi(pageId);
    if (res.code === 200 && res.data) {
      pageData.value = res.data;
    } else {
      pageData.value = null; // 确保在出错时清空数据
    }
  } catch (error) {
    console.error("获取页面详情失败:", error);
    pageData.value = null;
  } finally {
    loading.value = false;
  }
});
</script>

<style lang="scss" scoped>
/* ========== [核心修改] 复用资讯详情页的样式 ========== */

.page-container {
  height: 100vh;
  background-color: #FFFFFF;
}

/* 错误状态样式 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #FFFFFF;
  .empty-icon { font-size: 80rpx; margin-bottom: 24rpx; opacity: 0.5; }
  .empty-title { font-size: 30rpx; font-weight: 500; color: #606266; margin-bottom: 12rpx; }
  .empty-desc { font-size: 26rpx; color: #909399; }
}

/* 自定义导航栏 */
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  background-color: #FFFFFF;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.scrollable-content {
  height: 100%;
  box-sizing: border-box;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.status-bar {
  width: 100%;
}

.custom-nav-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.nav-back-button {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
}

.nav-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #000000;
  /* 标题过长时截断 */
  max-width: 60vw;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 页面主内容区 */
.main-content {
  padding: 0;
  background-color: #FFFFFF;
}

.article-title {
  font-size: 44rpx;
  color: #23232A;
  font-weight: 700;
  line-height: 1.5;
  padding: 40rpx 30rpx 0 30rpx;
  margin-bottom: 24rpx;
}

.article-meta-new {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  margin-bottom: 40rpx;

  .meta-left {
    display: flex;
    align-items: center;
    gap: 24rpx;
  }

  .meta-text {
    font-size: 24rpx;
    color: #9B9A9A;
  }
}

.content-card {
  padding: 0 30rpx 40rpx 30rpx;
}

.content-body {
  font-size: 32rpx;
  color: #333;
  line-height: 1.8;

  // 注入到 mp-html 的样式由外部 js 文件控制，此处无需再写 :deep
}
</style>