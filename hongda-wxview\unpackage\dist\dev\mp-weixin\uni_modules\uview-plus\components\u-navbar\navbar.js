"use strict";
const uni_modules_uviewPlus_libs_config_color = require("../../libs/config/color.js");
const Navbar = {
  // navbar 组件
  navbar: {
    safeAreaInsetTop: true,
    placeholder: false,
    fixed: true,
    border: false,
    leftIcon: "arrow-left",
    leftText: "",
    rightText: "",
    rightIcon: "",
    title: "",
    titleColor: "",
    bgColor: "#ffffff",
    titleWidth: "400rpx",
    height: "44px",
    leftIconSize: 20,
    leftIconColor: uni_modules_uviewPlus_libs_config_color.color.mainColor,
    autoBack: false,
    titleStyle: ""
  }
};
exports.Navbar = Navbar;
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/uni_modules/uview-plus/components/u-navbar/navbar.js.map
