package com.hongda.platform.mapper;

import java.util.List;
import com.hongda.platform.domain.HongdaPage;

/**
 * 页面管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-15
 */
public interface HongdaPageMapper 
{
    /**
     * 查询页面管理
     * 
     * @param id 页面管理主键
     * @return 页面管理
     */
    public HongdaPage selectHongdaPageById(Long id);

    /**
     * 查询页面管理列表
     * 
     * @param hongdaPage 页面管理
     * @return 页面管理集合
     */
    public List<HongdaPage> selectHongdaPageList(HongdaPage hongdaPage);

    /**
     * 新增页面管理
     * 
     * @param hongdaPage 页面管理
     * @return 结果
     */
    public int insertHongdaPage(HongdaPage hongdaPage);

    /**
     * 修改页面管理
     * 
     * @param hongdaPage 页面管理
     * @return 结果
     */
    public int updateHongdaPage(HongdaPage hongdaPage);

    /**
     * 删除页面管理
     * 
     * @param id 页面管理主键
     * @return 结果
     */
    public int deleteHongdaPageById(Long id);

    /**
     * 批量删除页面管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHongdaPageByIds(Long[] ids);
}
