<script>
// 1. 导入API方法 (保留，用于加载图片等其他资源)
import { getAllAssets } from '@/api/platform/asset.js';

export default {
  onLaunch: function () {
    console.log('App Launch');

    // 【优化】分离资源加载和字体加载
    this.fetchAndCacheAssets(); // 继续负责图片等后台资源
    this.loadCdnFonts();        // 【新增】专门负责从CDN加载所有字体
    this.loadIconFont();        // 保留uview-plus图标字体的加载
  },
  onShow: function () {
    console.log('App Show');
  },
  onHide: function () {
    console.log('App Hide');
  },
  methods: {
    /**
     * 【新增】从CDN加载所有自定义字体
     * 由于后台系统只能上传文件，我们在此处直接指定稳定高效的jsDelivr CDN地址
     */
    loadCdnFonts() {
      const fontsToLoad = [
        {
          family: 'Alibaba PuHuiTi 3.0',
          // 阿里巴巴普惠体】jsDelivr CDN地址
          source: 'url("https://cdn.jsdelivr.net/gh/xiahezaixiamen/my-fonts/AlibabaPuHuiTi-3-55-Regular.woff2")'
        },
        {
          family: 'YouSheBiaoTiHei',
          //优设标题黑】jsDelivr CDN地址
          source: 'url("https://cdn.jsdelivr.net/gh/xiahezaixiamen/my-fonts/YouSheBiaoTiHei.woff2")'
        }
      ];

      let loadedCount = 0;
      const totalFonts = fontsToLoad.length;

      fontsToLoad.forEach(font => {
        uni.loadFontFace({
          global: true,
          family: font.family,
          source: font.source,
          success: () => {
            console.log(`CDN字体 [${font.family}] 加载成功!`);
            loadedCount++;

            // 当所有字体都加载完成时，触发全局事件和设置全局状态
            if (loadedCount === totalFonts) {
              // 设置全局状态
              this.globalData = this.globalData || {};
              this.globalData.fontLoaded = true;

              // 触发全局事件，通知所有组件字体已加载完成
              uni.$emit('customFontsLoaded');
              console.log('所有自定义字体加载完成，已触发全局事件');
            }
          },
          fail(err) {
            console.error(`CDN字体 [${font.family}] 加载失败:`, err);
            loadedCount++;

            // 即使有字体加载失败，也要检查是否所有字体都处理完了
            if (loadedCount === totalFonts) {
              this.globalData = this.globalData || {};
              this.globalData.fontLoaded = true;
              uni.$emit('customFontsLoaded');
              console.log('字体加载处理完成（部分可能失败），已触发全局事件');
            }
          }
        });
      });
    },

    /**
     * 【保留并优化】获取、缓存静态资源（不再负责字体）
     * 此函数继续为App中的图片、背景等资源服务
     */
    async fetchAndCacheAssets() {
      try {
        const response = await getAllAssets();
        if (response.code === 200 && Array.isArray(response.data)) {
          const assetMap = response.data.reduce((map, item) => {
            if (item.assetKey && item.assetUrl) {
              map[item.assetKey] = item.assetUrl;
            }
            return map;
          }, {});

          uni.setStorageSync('staticAssets', assetMap);
          console.log('小程序图片等静态资源已更新并缓存成功！');
          // 【已移除】不再从此函数中调用旧的字体加载方法
        }
      } catch (error) {
        console.error('获取小程序静态资源失败', error);
      }
    },

    /**
     * 【已废弃】loadDynamicFonts 方法可以安全删除了，其功能已被 loadCdnFonts 替代
     */
    
    /**
     * 加载uView图标字体 (保留)
     */
    loadIconFont() {
      // #ifdef APP || H5 || MP-WEIXIN || MP-ALIPAY
      uni.loadFontFace({
        global: true,
        family: 'uicon-iconfont',
        source: 'url("https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf")',
        success() {
          console.log('uview-plus图标字体加载成功');
        },
        fail(err) {
          console.error('uview-plus图标字体加载失败:', err);
          uni.loadFontFace({
            global: true,
            family: 'uicon-iconfont',
            source: 'url("https://cdn.jsdelivr.net/npm/uview-plus@3.1.37/fonts/uicon-iconfont.ttf")',
            success() {
              console.log('备用图标字体加载成功');
            },
            fail(err2) {
              console.error('备用图标字体也加载失败:', err2);
            }
          });
        }
      });
      // #endif
    },
  }
}
</script>

<style lang="scss">
/* 每个页面公共css */
@import "@/uni_modules/uview-plus/index.scss";

/* 防止页面跳转时内容残留 */
uni-page-wrapper {
  overflow: hidden !important;
}
uni-page-body {
  overflow: hidden !important;
}

/* 【修改】更新全局字体栈，将新字体作为最高优先级 */
body, page, view, text {
  font-family: 'Alibaba PuHuiTi 3.0', sans-serif;
}
</style>
