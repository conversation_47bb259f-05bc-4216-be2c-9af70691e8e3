/**
 * 统一导航跳转工具 (推荐版)
 * @description 本函数与优化后的后端配套使用，接收包含 linkType 和 linkTarget 的对象。
 * @param {object} navItem - 包含 linkType 和 linkTarget 的导航对象
 */
export function navigateTo(navItem) {
    // 检查从后端获取的数据是否有效
    if (!navItem || !navItem.linkType || !navItem.linkTarget) {
        console.warn('无效的导航项，缺少 linkType 或 linkTarget:', navItem);
        return;
    }

    const { linkType, linkTarget, title } = navItem;

    // 根据后端指定的类型执行跳转，前端无需猜测
    switch (linkType) {
        case 'INTERNAL_PAGE': // 内部普通页面
            uni.navigateTo({
                url: linkTarget,
                fail: (err) => console.error(`跳转普通页面失败: ${linkTarget}`, err)
            });
            break;

        case 'TAB_PAGE': // Tab栏页面
            uni.switchTab({
                url: linkTarget,
                fail: (err) => console.error(`跳转 TabBar 失败: ${linkTarget}`, err)
            });
            break;

        case 'EXTERNAL_LINK': // 外部H5链接
            // 确保项目中存在 /pages/webview/index 页面
            uni.navigateTo({
                url: `/pages/webview/index?url=${encodeURIComponent(linkTarget)}&title=${title || ''}`,
                fail: (err) => console.error(`跳转 Webview 失败: ${linkTarget}`, err)
            });
            break;

        default:
            console.warn('未知的链接类型:', linkType);
            break;
    }
}