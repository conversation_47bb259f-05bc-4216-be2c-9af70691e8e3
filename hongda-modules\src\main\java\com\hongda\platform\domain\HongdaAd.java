package com.hongda.platform.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hongda.common.annotation.Excel;
import com.hongda.common.annotation.OssUrl;
import com.hongda.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Transient;

import java.util.Date;

/**
 * 广告管理对象 hongda_ad
 */
@Getter
@Setter
@Schema(description = "广告管理")
public class HongdaAd extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 广告ID */
    @Schema(description = "广告ID", example = "1")
    private Long id;

    /** 广告标题 */
    @Excel(name = "广告标题")
    @Schema(description = "广告标题", example = "红大走出去宣传广告")
    private String title;

    /** 广告位代码 */
    @Excel(name = "广告位代码")
    @Schema(description = "广告位代码", example = "HOME_BANNER")
    private String positionCode;

    /** 广告图片链接 */
    @Excel(name = "广告图片链接")
    @Schema(description = "广告图片链接", example = "https://example.com/banner.jpg")
    @OssUrl
    private String imageUrl;

    /** 关联的内容页面ID */
    @Excel(name = "关联页面ID")
    @Schema(description = "关联的内容页面ID", example = "101")
    private Integer pageId;

    /** 跳转链接 */
    @Schema(description = "跳转链接 (自定义)", example = "https://example.com/activity")
    private String linkUrl;

    /** 关联的活动ID */
    @Schema(description = "关联的活动ID", example = "123")
    private Long relatedEventId;

    /** 生效开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生效开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    @Schema(description = "生效开始时间", example = "2025-07-20")
    private Date startTime;

    /** 生效结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生效结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    @Schema(description = "生效结束时间", example = "2025-12-31")
    private Date endTime;

    /** 排序 */
    @Excel(name = "排序")
    @Schema(description = "排序", example = "1")
    private Long sortOrder;

    /** 状态 */
    @Excel(name = "状态")
    @Schema(description = "状态", example = "1", allowableValues = {"0", "1"})
    private Integer status;

    /** 关联的页面标题 (非数据表字段) */
    @Schema(description = "关联的页面标题 (仅用于列表展示)")
    @Transient
    private String pageTitle;

    // Lombok 会自动生成所有 Getter/Setter
}