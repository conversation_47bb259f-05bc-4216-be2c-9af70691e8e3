"use strict";
const common_vendor = require("../../../../common/vendor.js");
const uni_modules_uviewPlus_libs_config_config = require("../../libs/config/config.js");
function once(fn) {
  let called = false;
  let result;
  return function(...args) {
    if (!called) {
      result = fn.apply(this, args);
      called = true;
    }
    return result;
  };
}
const loadFont = once(() => {
  common_vendor.index.loadFontFace({
    global: true,
    // 是否全局生效。微信小程序 '2.10.0'起支持全局生效，需在 app.vue 中调用。
    family: "uicon-iconfont",
    source: 'url("' + uni_modules_uviewPlus_libs_config_config.config.iconUrl + '")',
    success() {
    },
    fail() {
    }
  });
  if (uni_modules_uviewPlus_libs_config_config.config.customIcon.family) {
    common_vendor.index.loadFontFace({
      global: true,
      // 是否全局生效。微信小程序 '2.10.0'起支持全局生效，需在 app.vue 中调用。
      family: uni_modules_uviewPlus_libs_config_config.config.customIcon.family,
      source: 'url("' + uni_modules_uviewPlus_libs_config_config.config.customIcon.url + '")',
      success() {
      },
      fail() {
      }
    });
  }
  return true;
});
let fontUtil = {
  loadFont
};
exports.fontUtil = fontUtil;
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/uni_modules/uview-plus/components/u-icon/util.js.map
