package com.hongda.wxapp.service;

import java.util.List;
import java.util.Map;
import com.hongda.content.domain.HongdaEvent;

/**
 * 小程序活动Service接口
 * 
 * <AUTHOR>
 */
public interface IWxEventService 
{
    /**
     * 查询活动列表（小程序专用）
     * 
     * @param hongdaEvent 查询条件
     * @param startTime 开始时间范围
     * @param endTime 结束时间范围
     * @return 活动列表
     */
    public List<HongdaEvent> selectEventList(HongdaEvent hongdaEvent, String startTime, String endTime);

    /**
     * 查询活动详情
     * 
     * @param id 活动ID
     * @return 活动详情
     */
    public HongdaEvent selectEventById(Long id);

    /**
     * 获取热门活动列表
     * 
     * @param limit 限制数量
     * @return 热门活动列表
     */
    public List<HongdaEvent> selectHotEvents(Integer limit);

    /**
     * 获取即将开始的活动列表
     * 
     * @param limit 限制数量
     * @return 即将开始的活动列表
     */
    public List<HongdaEvent> selectUpcomingEvents(Integer limit);

    /**
     * 获取所有不重复的活动地区
     * 
     * @return 地区列表
     */
    public List<String> getDistinctLocations();

    /**
     * 根据地区统计活动数量
     * 
     * @return 地区活动数量映射
     */
    public Map<String, Long> getLocationStatistics();

    /**
     * 搜索活动
     * 
     * @param keyword 搜索关键词
     * @return 活动列表
     */
    public List<HongdaEvent> searchEvents(String keyword);

    /**
     * 按地区获取活动列表
     * 
     * @param location 地区名称
     * @return 活动列表
     */
    public List<HongdaEvent> selectEventsByLocation(String location);

    /**
     * 获取活动的表单定义
     * 
     * @param eventId 活动ID
     * @return 表单定义对象
     */
    public Object getEventFormDefinition(Long eventId);

    /**
     * 查询活动列表（支持综合排序和时间范围筛选）
     * 
     * @param hongdaEvent 查询条件
     * @param startTime 开始时间范围
     * @param endTime 结束时间范围
     * @param timeRangeStart 时间筛选范围开始
     * @param timeRangeEnd 时间筛选范围结束
     * @param orderBy 排序字段
     * @param isAsc 是否升序
     * @return 活动列表
     */
    public List<HongdaEvent> selectEventListWithSort(HongdaEvent hongdaEvent, String startTime, String endTime, String timeRangeStart, String timeRangeEnd, String orderBy, String isAsc, Integer registrationStatus);

    /**
     * 获取日历视图活动列表
     * 专门为日历视图优化：只返回状态0(未开始)和1(报名中)的活动，按开始时间升序排序（越早的越在前面）
     * 注意：此方法返回所有符合条件的活动，分页逻辑在Controller层处理
     * 
     * @param hongdaEvent 查询条件
     * @param timeRangeStart 时间筛选范围开始
     * @param timeRangeEnd 时间筛选范围结束
     * @return 活动列表（未分页）
     */
    public List<HongdaEvent> selectCalendarEvents(HongdaEvent hongdaEvent, String timeRangeStart, String timeRangeEnd);

    /**
     * 获取所有不重复的活动城市
     * 
     * @return 城市列表
     */
    public List<String> getDistinctCities();

    /**
     * 获取推广活动列表
     * 
     * @return 推广活动列表
     */
    public List<HongdaEvent> selectPromotionEventList();
} 