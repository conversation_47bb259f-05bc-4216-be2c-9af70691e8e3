<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="广告标题" prop="title">
        <el-input
            v-model="queryParams.title"
            placeholder="请输入广告标题"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="广告位代码" prop="positionCode">
        <el-select v-model="queryParams.positionCode" placeholder="请选择广告位代码" clearable style="width: 200px">
          <el-option
              v-for="dict in hongda_ad_position"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 150px;">
          <el-option
              v-for="dict in hongda_common_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['platform:ad:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['platform:ad:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['platform:ad:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['platform:ad:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="adList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="广告ID" align="center" prop="id" />
      <el-table-column label="广告标题" align="center" prop="title" width="200" :show-overflow-tooltip="true" />
      <el-table-column label="广告图片" align="center" prop="imageUrl" width="100">
        <template #default="scope">
          <image-preview :src="scope.row.imageUrl" :width="50" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="广告位" align="center" prop="positionCode">
        <template #default="scope">
          <dict-tag :options="hongda_ad_position" :value="scope.row.positionCode"/>
        </template>
      </el-table-column>

      <el-table-column label="关联目标" align="center" width="220" :show-overflow-tooltip="true">
        <template #default="scope">
          <div v-if="scope.row.pageId">
            <el-tag type="success">页面</el-tag>
            <span style="margin-left: 5px;">{{ scope.row.pageTitle }}</span>
          </div>
          <div v-else-if="scope.row.relatedEventId">
            <el-tag type="warning">活动</el-tag>
            <span style="margin-left: 5px;">{{ scope.row.eventTitle }}</span>
          </div>
          <div v-else-if="scope.row.linkUrl">
            <el-tag type="primary">链接</el-tag>
            <span style="margin-left: 5px;">{{ scope.row.linkUrl }}</span>
          </div>
          <span v-else>无</span>
        </template>
      </el-table-column>

      <el-table-column label="生效时间" align="center" prop="startTime" width="120" sortable>
        <template #default="scope">
          <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="失效时间" align="center" prop="endTime" width="120" sortable>
        <template #default="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="sortOrder" />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="hongda_common_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
        <template #default="scope">
          <el-dropdown trigger="click">
            <el-button link type="primary">
              更多操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                    icon="Edit"
                    @click="handleUpdate(scope.row)"
                    v-hasPermi="['platform:ad:edit']">
                  修改
                </el-dropdown-item>
                <el-dropdown-item
                    icon="Delete"
                    @click="handleDelete(scope.row)"
                    v-hasPermi="['platform:ad:remove']"
                    :style="{ color: 'var(--el-color-danger)' }">
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <el-dialog :title="title" v-model="open" width="700px" append-to-body>
      <el-form ref="adRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="广告标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入广告标题" />
        </el-form-item>
        <el-form-item label="广告位" prop="positionCode">
          <el-select v-model="form.positionCode" placeholder="请选择广告位">
            <el-option
                v-for="dict in hongda_ad_position"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="广告图片" prop="imageUrl">
          <image-upload v-model="form.imageUrl"/>
        </el-form-item>

        <el-form-item label="关联页面" prop="pageId">
          <el-select
              v-model="form.pageId"
              placeholder="推荐，可链接到系统内所有页面"
              filterable
              clearable
              style="width: 100%"
              :disabled="!!form.relatedEventId || !!form.linkUrl"
              @change="handlePageChange"
          >
            <el-option
                v-for="item in pageOptions"
                :key="item.id"
                :label="item.title"
                :value="item.id"
            />
          </el-select>
          <div class="form-help-text">
            <strong>首选方案。</strong>用于链接到【页面管理】中创建的可复用页面。
          </div>
        </el-form-item>

        <el-form-item label="关联活动" prop="relatedEventId">
          <el-select
              v-model="form.relatedEventId"
              placeholder="请搜索并选择具体活动"
              filterable
              clearable
              style="width: 100%"
              :disabled="!!form.pageId || !!form.linkUrl"
              @change="handleEventChange"
          >
            <el-option
                v-for="item in eventOptions"
                :key="item.id"
                :label="item.title"
                :value="item.id"
            />
          </el-select>
          <div class="form-help-text">
            <strong>专用方案。</strong>当广告需直接跳转到某个具体活动时选择此项。
          </div>
        </el-form-item>

        <el-form-item label="临时/外部链接" prop="linkUrl">
          <el-input
              v-model="form.linkUrl"
              type="textarea"
              placeholder="备用项，用于一次性外部链接"
              :disabled="!!form.pageId || !!form.relatedEventId"
              @input="handleLinkUrlInput"
          />
          <div class="form-help-text">
            <strong>备用方案。</strong>用于跳转到外部网站或一次性H5页。**若已选择上方任一项，此项无效。**
          </div>
        </el-form-item>
        <el-form-item label="生效时间" prop="startTime">
          <el-date-picker clearable
                          v-model="form.startTime"
                          type="datetime"
                          value-format="YYYY-MM-DD HH:mm:ss"
                          placeholder="留空表示立即生效">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="失效时间" prop="endTime">
          <el-date-picker clearable
                          v-model="form.endTime"
                          type="datetime"
                          value-format="YYYY-MM-DD HH:mm:ss"
                          placeholder="留空表示永不失效">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="form.sortOrder" controls-position="right" :min="0" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
                v-for="dict in hongda_common_status"
                :key="dict.value"
                :label="parseInt(dict.value)"
            >{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Ad">
import { listAd, getAd, delAd, addAd, updateAd } from "@/api/platform/ad";
import { listPage } from "@/api/platform/page";
import { listEvent } from "@/api/content/event"; // 确保路径正确

const { proxy } = getCurrentInstance();
const { hongda_common_status, hongda_ad_position } = proxy.useDict('hongda_common_status', 'hongda_ad_position');

const adList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const pageOptions = ref([]);
const eventOptions = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    title: null,
    positionCode: null,
    status: null,
  },
  rules: {
    title: [
      { required: true, message: "广告标题不能为空", trigger: "blur" }
    ],
    positionCode: [
      { required: true, message: "广告位代码不能为空", trigger: "change" }
    ],
    imageUrl: [
      { required: true, message: "广告图片链接不能为空", trigger: "blur" }
    ],
    status: [
      { required: true, message: "状态不能为空", trigger: "change" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 获取所有页面列表 */
function getPageList() {
  listPage({ pageNum: 1, pageSize: 9999 }).then(response => {
    pageOptions.value = response.rows;
  });
}

/** 获取所有活动列表 */
function getEventList() {
  listEvent({ pageNum: 1, pageSize: 9999 }).then(response => {
    eventOptions.value = response.rows;
  });
}

/** 查询广告管理列表 */
function getList() {
  loading.value = true;
  listAd(queryParams.value).then(response => {
    adList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    title: null,
    positionCode: null,
    imageUrl: null,
    pageId: null,
    linkUrl: null,
    relatedEventId: null,
    startTime: null,
    endTime: null,
    sortOrder: 0,
    status: 1,
  };
  proxy.resetForm("adRef");
}

/** 处理关联页面的变化，自动清空其他链接选项 */
function handlePageChange(value) {
  if (value) {
    form.value.relatedEventId = null;
    form.value.linkUrl = '';
  }
}

/** 处理关联活动的变化，自动清空其他链接选项 */
function handleEventChange(value) {
  if (value) {
    form.value.pageId = null;
    form.value.linkUrl = '';
  }
}

/** 处理自定义链接的输入，自动清空其他链接选项 */
function handleLinkUrlInput(value) {
  if (value) {
    form.value.pageId = null;
    form.value.relatedEventId = null;
  }
}


/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  getPageList();
  getEventList();
  open.value = true;
  title.value = "添加广告";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  getPageList();
  getEventList();
  const _id = row.id || ids.value;
  getAd(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改广告";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["adRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateAd(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addAd(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除广告管理编号为"' + _ids + '"的数据项？').then(function() {
    return delAd(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('platform/ad/export', {
    ...queryParams.value
  }, `ad_${new Date().getTime()}.xlsx`);
}

getList();
</script>

<style scoped>
.form-help-text {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.5;
}
</style>